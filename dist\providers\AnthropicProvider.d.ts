import { LL<PERSON>rovider, LLMO<PERSON>s, LLMResponse, <PERSON><PERSON><PERSON>all, <PERSON>lR<PERSON>ult, AgentContext } from '@/types';
export declare class AnthropicProvider implements LLMProvider {
    readonly name = "anthropic";
    private client;
    private logger;
    private toolRegistry;
    constructor(config: any);
    generateResponse(prompt: string, options?: LLMOptions): Promise<LLMResponse>;
    generateStreamResponse(prompt: string, options?: LLMOptions): AsyncGenerator<string, void, unknown>;
    supportsToolCalling(): boolean;
    callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult>;
    private buildMessages;
    validateApiKey(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    getDefaultModel(): string;
    getMaxTokens(model?: string): number;
    estimateTokens(text: string): number;
    calculateCost(usage: {
        promptTokens: number;
        completionTokens: number;
    }, model?: string): number;
}
//# sourceMappingURL=AnthropicProvider.d.ts.map