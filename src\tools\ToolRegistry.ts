import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tool<PERSON>all } from '@/types';
import { ToolExecutionError } from '@/utils/ErrorHandler';
import { Logger } from '@/utils/Logger';
import { ShellTool } from './ShellTool';
import { FileTool } from './FileTool';

export class ToolRegistry {
  private static instance: ToolRegistry;
  private tools: Map<string, Tool>;
  private logger: Logger;
  private executionQueue: Map<string, Promise<ToolResult>>;

  private constructor() {
    this.tools = new Map();
    this.logger = Logger.getInstance();
    this.executionQueue = new Map();
    this.registerDefaultTools();
  }

  public static getInstance(): ToolRegistry {
    if (!ToolRegistry.instance) {
      ToolRegistry.instance = new ToolRegistry();
    }
    return ToolRegistry.instance;
  }

  private registerDefaultTools(): void {
    this.registerTool(new ShellTool());
    this.registerTool(new FileTool());
  }

  public registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
    this.logger.info(`Tool registered: ${tool.name}`, { 
      toolName: tool.name,
      description: tool.description 
    });
  }

  public unregisterTool(toolName: string): boolean {
    const removed = this.tools.delete(toolName);
    if (removed) {
      this.logger.info(`Tool unregistered: ${toolName}`);
    }
    return removed;
  }

  public getTool(toolName: string): Tool | undefined {
    return this.tools.get(toolName);
  }

  public getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  public getToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  public hasToolCalling(): boolean {
    return this.tools.size > 0;
  }

  public async executeTool(
    toolCall: ToolCall,
    context: AgentContext
  ): Promise<ToolResult> {
    const tool = this.getTool(toolCall.name);
    
    if (!tool) {
      throw new ToolExecutionError(
        toolCall.name,
        `Tool not found: ${toolCall.name}`,
        { availableTools: this.getToolNames() }
      );
    }

    try {
      this.logger.info(`Executing tool: ${toolCall.name}`, {
        toolName: toolCall.name,
        arguments: toolCall.arguments,
        toolCallId: toolCall.id
      });

      const startTime = Date.now();
      const result = await tool.execute(toolCall.arguments, context);
      const duration = Date.now() - startTime;

      this.logger.info(`Tool execution completed: ${toolCall.name}`, {
        toolName: toolCall.name,
        success: result.success,
        duration,
        toolCallId: toolCall.id
      });

      return {
        ...result,
        metadata: {
          ...result.metadata,
          toolCallId: toolCall.id,
          executionTime: duration
        }
      };

    } catch (error) {
      this.logger.error(`Tool execution failed: ${toolCall.name}`, {
        toolName: toolCall.name,
        error: (error as Error).message,
        arguments: toolCall.arguments,
        toolCallId: toolCall.id
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        toolCall.name,
        (error as Error).message,
        { 
          arguments: toolCall.arguments,
          toolCallId: toolCall.id 
        }
      );
    }
  }

  public async executeToolsParallel(
    toolCalls: ToolCall[],
    context: AgentContext,
    maxParallel: number = 3
  ): Promise<ToolResult[]> {
    if (toolCalls.length === 0) {
      return [];
    }

    this.logger.info(`Executing ${toolCalls.length} tools in parallel`, {
      toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id })),
      maxParallel
    });

    const results: ToolResult[] = [];
    const executing: Promise<{ index: number; result: ToolResult }>[] = [];

    for (let i = 0; i < toolCalls.length; i++) {
      const toolCall = toolCalls[i];
      
      // Wait if we've reached the parallel limit
      if (executing.length >= maxParallel) {
        const completed = await Promise.race(executing);
        results[completed.index] = completed.result;
        
        // Remove completed promise from executing array
        const completedIndex = executing.findIndex(p => p === Promise.resolve(completed));
        if (completedIndex !== -1) {
          executing.splice(completedIndex, 1);
        }
      }

      // Start new tool execution
      const executionPromise = this.executeTool(toolCall, context)
        .then(result => ({ index: i, result }))
        .catch(error => ({ 
          index: i, 
          result: { 
            success: false, 
            result: null, 
            error: error.message,
            metadata: { toolCallId: toolCall.id }
          } 
        }));

      executing.push(executionPromise);
    }

    // Wait for all remaining executions to complete
    const remainingResults = await Promise.all(executing);
    for (const { index, result } of remainingResults) {
      results[index] = result;
    }

    this.logger.info(`Parallel tool execution completed`, {
      totalTools: toolCalls.length,
      successfulTools: results.filter(r => r.success).length,
      failedTools: results.filter(r => !r.success).length
    });

    return results;
  }

  public async executeToolsSequential(
    toolCalls: ToolCall[],
    context: AgentContext
  ): Promise<ToolResult[]> {
    if (toolCalls.length === 0) {
      return [];
    }

    this.logger.info(`Executing ${toolCalls.length} tools sequentially`, {
      toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id }))
    });

    const results: ToolResult[] = [];

    for (const toolCall of toolCalls) {
      try {
        const result = await this.executeTool(toolCall, context);
        results.push(result);

        // If a tool fails and it's critical, we might want to stop
        if (!result.success && this.isCriticalTool(toolCall.name)) {
          this.logger.warn(`Critical tool failed, stopping sequential execution: ${toolCall.name}`);
          break;
        }
      } catch (error) {
        const errorResult: ToolResult = {
          success: false,
          result: null,
          error: (error as Error).message,
          metadata: { toolCallId: toolCall.id }
        };
        results.push(errorResult);

        // Stop on critical tool failure
        if (this.isCriticalTool(toolCall.name)) {
          this.logger.warn(`Critical tool failed, stopping sequential execution: ${toolCall.name}`);
          break;
        }
      }
    }

    this.logger.info(`Sequential tool execution completed`, {
      totalTools: toolCalls.length,
      executedTools: results.length,
      successfulTools: results.filter(r => r.success).length,
      failedTools: results.filter(r => !r.success).length
    });

    return results;
  }

  private isCriticalTool(toolName: string): boolean {
    // Define which tools are considered critical
    // If they fail, subsequent tools might not work properly
    const criticalTools = ['shell', 'file'];
    return criticalTools.includes(toolName);
  }

  public getToolSchema(toolName: string): any {
    const tool = this.getTool(toolName);
    if (!tool) {
      return null;
    }

    return {
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    };
  }

  public getAllToolSchemas(): any[] {
    return this.getAllTools().map(tool => this.getToolSchema(tool.name));
  }

  public validateToolCall(toolCall: ToolCall): boolean {
    const tool = this.getTool(toolCall.name);
    if (!tool) {
      return false;
    }

    // Basic validation - check if required parameters are present
    const required = tool.parameters.required || [];
    for (const param of required) {
      if (!(param in toolCall.arguments)) {
        this.logger.warn(`Missing required parameter: ${param} for tool: ${toolCall.name}`);
        return false;
      }
    }

    return true;
  }

  public getToolUsageStats(): Record<string, { calls: number; successes: number; failures: number }> {
    // This would be implemented with persistent storage in a real application
    // For now, return empty stats
    const stats: Record<string, { calls: number; successes: number; failures: number }> = {};

    for (const toolName of this.getToolNames()) {
      stats[toolName] = { calls: 0, successes: 0, failures: 0 };
    }

    return stats;
  }

  public async executeToolWithRetry(
    toolCall: ToolCall,
    context: AgentContext,
    maxRetries: number = 2
  ): Promise<ToolResult> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        const result = await this.executeTool(toolCall, context);

        if (result.success || attempt === maxRetries + 1) {
          return result;
        }

        // If not successful but not the last attempt, retry
        this.logger.warn(`Tool execution failed, retrying (${attempt}/${maxRetries + 1}): ${toolCall.name}`, {
          error: result.error,
          attempt
        });

        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));

      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries + 1) {
          throw error;
        }

        this.logger.warn(`Tool execution error, retrying (${attempt}/${maxRetries + 1}): ${toolCall.name}`, {
          error: (error as Error).message,
          attempt
        });

        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
      }
    }

    throw lastError || new ToolExecutionError(toolCall.name, 'Max retries exceeded');
  }

  public analyzeDependencies(toolCalls: ToolCall[]): ToolCall[][] {
    // Simple dependency analysis - group tools that can run in parallel
    const groups: ToolCall[][] = [];
    const fileOperations: ToolCall[] = [];
    const shellOperations: ToolCall[] = [];
    const independentOperations: ToolCall[] = [];

    for (const toolCall of toolCalls) {
      if (toolCall.name === 'file') {
        // File operations might depend on each other
        const operation = toolCall.arguments.operation;
        if (['read', 'exists', 'stat', 'search', 'glob'].includes(operation)) {
          // Read operations can run in parallel
          independentOperations.push(toolCall);
        } else {
          // Write operations should be sequential
          fileOperations.push(toolCall);
        }
      } else if (toolCall.name === 'shell') {
        // Shell operations might have dependencies
        shellOperations.push(toolCall);
      } else {
        // Other tools can run independently
        independentOperations.push(toolCall);
      }
    }

    // Add groups in dependency order
    if (independentOperations.length > 0) {
      groups.push(independentOperations);
    }
    if (fileOperations.length > 0) {
      groups.push(fileOperations);
    }
    if (shellOperations.length > 0) {
      groups.push(shellOperations);
    }

    return groups.length > 0 ? groups : [toolCalls];
  }

  private createBatches(toolCalls: ToolCall[], maxParallel: number): ToolCall[][] {
    const batches: ToolCall[][] = [];
    for (let i = 0; i < toolCalls.length; i += maxParallel) {
      batches.push(toolCalls.slice(i, i + maxParallel));
    }
    return batches;
  }

  public async executeToolChain(
    toolCalls: ToolCall[],
    context: AgentContext,
    options: {
      stopOnFailure?: boolean;
      maxParallel?: number;
      enableRetry?: boolean;
    } = {}
  ): Promise<ToolResult[]> {
    const {
      stopOnFailure = false,
      maxParallel = 3,
      enableRetry = true
    } = options;

    this.logger.info(`Executing tool chain with ${toolCalls.length} tools`, {
      stopOnFailure,
      maxParallel,
      enableRetry
    });

    const results: ToolResult[] = [];
    const dependencyGroups = this.analyzeDependencies(toolCalls);

    for (const group of dependencyGroups) {
      const batches = this.createBatches(group, maxParallel);

      for (const batch of batches) {
        const batchPromises = batch.map(toolCall =>
          enableRetry ?
            this.executeToolWithRetry(toolCall, context, 2) :
            this.executeTool(toolCall, context)
        );

        const batchResults = await Promise.allSettled(batchPromises);

        for (let i = 0; i < batchResults.length; i++) {
          const result = batchResults[i];
          const toolCall = batch[i];

          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            const failedResult: ToolResult = {
              success: false,
              result: null,
              error: result.reason?.message || 'Tool execution failed',
              metadata: {
                toolName: toolCall.name,
                toolCallId: toolCall.id
              }
            };
            results.push(failedResult);

            if (stopOnFailure) {
              this.logger.error(`Tool chain stopped due to failure: ${toolCall.name}`);
              return results;
            }
          }
        }
      }
    }

    return results;
  }
}
