import winston from 'winston';
import path from 'path';
import fs from 'fs-extra';
import { LogEntry, LogLevel } from '@/types';

export class Logger {
  private static instance: Logger;
  private logger!: winston.Logger;
  private sessionId?: string;

  private constructor() {
    this.initializeLogger();
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private initializeLogger(): void {
    const logsDir = path.join(process.cwd(), '.ai-cli', 'logs');
    fs.ensureDirSync(logsDir);

    const logFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, sessionId, ...meta }) => {
        const logEntry: LogEntry = {
          level: level as keyof LogLevel,
          message: String(message),
          timestamp: new Date(String(timestamp)),
          sessionId: sessionId ? String(sessionId) : undefined,
          metadata: Object.keys(meta).length > 0 ? meta : undefined
        };
        return JSON.stringify(logEntry);
      })
    );

    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat,
      transports: [
        new winston.transports.File({
          filename: path.join(logsDir, 'error.log'),
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5
        }),
        new winston.transports.File({
          filename: path.join(logsDir, 'combined.log'),
          maxsize: 5242880, // 5MB
          maxFiles: 10
        }),
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ level, message, timestamp }) => {
              return `${timestamp} [${level}]: ${message}`;
            })
          )
        })
      ]
    });
  }

  public setSessionId(sessionId: string): void {
    this.sessionId = sessionId;
  }

  public error(message: string, metadata?: Record<string, any>): void {
    this.logger.error(message, { sessionId: this.sessionId, ...metadata });
  }

  public warn(message: string, metadata?: Record<string, any>): void {
    this.logger.warn(message, { sessionId: this.sessionId, ...metadata });
  }

  public info(message: string, metadata?: Record<string, any>): void {
    this.logger.info(message, { sessionId: this.sessionId, ...metadata });
  }

  public debug(message: string, metadata?: Record<string, any>): void {
    this.logger.debug(message, { sessionId: this.sessionId, ...metadata });
  }

  public verbose(message: string, metadata?: Record<string, any>): void {
    this.logger.verbose(message, { sessionId: this.sessionId, ...metadata });
  }

  public logToolExecution(toolName: string, args: any, result: any, duration: number): void {
    this.info(`Tool executed: ${toolName}`, {
      tool: toolName,
      arguments: args,
      result: result,
      duration,
      type: 'tool_execution'
    });
  }

  public logLLMCall(provider: string, model: string, prompt: string, response: string, usage?: any): void {
    this.info(`LLM call: ${provider}/${model}`, {
      provider,
      model,
      promptLength: prompt.length,
      responseLength: response.length,
      usage,
      type: 'llm_call'
    });
  }

  public logSessionEvent(event: string, metadata?: Record<string, any>): void {
    this.info(`Session event: ${event}`, {
      event,
      sessionId: this.sessionId,
      type: 'session_event',
      ...metadata
    });
  }

  public logError(error: Error, context?: string): void {
    this.error(`${context ? `${context}: ` : ''}${error.message}`, {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context,
      type: 'error'
    });
  }
}
