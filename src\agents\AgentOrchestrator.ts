import { <PERSON><PERSON><PERSON><PERSON>, AgentContext, Message, ToolCall, ToolResult, LLMResponse } from '@/types';
import { Logger } from '@/utils/Logger';
import { ConfigManager } from '@/config/ConfigManager';
import { LLMProviderFactory } from '@/providers/LLMProviderFactory';
import { ToolRegistry } from '@/tools/ToolRegistry';
import { SessionManager } from '@/session/SessionManager';
import { ContextEngine } from '@/context/ContextEngine';
import { LLMProviderError } from '@/utils/ErrorHandler';

export class AgentOrchestrator {
  private static instance: AgentOrchestrator;
  private logger: Logger;
  private configManager: ConfigManager;
  private llmProviderFactory: LLMProviderFactory;
  private toolRegistry: ToolRegistry;
  private sessionManager: SessionManager;
  private contextEngine: ContextEngine;
  private currentProvider: LLMProvider | null;

  private constructor() {
    this.logger = Logger.getInstance();
    this.configManager = ConfigManager.getInstance();
    this.llmProviderFactory = LLMProviderFactory.getInstance();
    this.toolRegistry = ToolRegistry.getInstance();
    this.sessionManager = SessionManager.getInstance();
    this.contextEngine = ContextEngine.getInstance();
    this.currentProvider = null;
  }

  public static getInstance(): AgentOrchestrator {
    if (!AgentOrchestrator.instance) {
      AgentOrchestrator.instance = new AgentOrchestrator();
    }
    return AgentOrchestrator.instance;
  }

  public async initialize(workingDirectory: string): Promise<AgentContext> {
    try {
      this.logger.info('Initializing Agent Orchestrator', { workingDirectory });

      // Initialize LLM provider
      const config = this.configManager.getAgentConfig();
      this.currentProvider = await this.llmProviderFactory.getProvider(config.provider);

      // Create agent context
      const sessionId = 'session-' + Date.now();
      const agentContext = await this.contextEngine.createAgentContext(sessionId, workingDirectory);

      // Create and set session
      const session = await this.sessionManager.createSession(workingDirectory, agentContext);
      this.logger.setSessionId(session.id);

      this.logger.info('Agent Orchestrator initialized successfully', {
        sessionId: session.id,
        provider: config.provider,
        model: config.model,
        projectType: agentContext.projectContext.projectType
      });

      return agentContext;

    } catch (error) {
      this.logger.error('Failed to initialize Agent Orchestrator', {
        error: (error as Error).message,
        workingDirectory
      });
      throw error;
    }
  }

  public async processUserInput(
    input: string,
    context: AgentContext,
    options: {
      streaming?: boolean;
      maxIterations?: number;
      enableToolCalling?: boolean;
    } = {}
  ): Promise<string> {
    const maxIterations = options.maxIterations || 5;
    const enableToolCalling = options.enableToolCalling ?? this.configManager.getAgentConfig().enableToolCalling;
    
    try {
      this.logger.info('Processing user input', {
        inputLength: input.length,
        sessionId: context.sessionId,
        streaming: options.streaming,
        enableToolCalling
      });

      // Add user message to conversation history
      const userMessage: Message = {
        role: 'user',
        content: input
      };
      this.sessionManager.addMessage(userMessage);

      let response = '';
      let iteration = 0;

      while (iteration < maxIterations) {
        iteration++;

        this.logger.debug(`Agent iteration ${iteration}/${maxIterations}`);

        // Get LLM response
        const llmResponse = await this.getLLMResponse(input, context, enableToolCalling);
        
        // Add assistant message to conversation
        if (llmResponse.content) {
          const assistantMessage: Message = {
            role: 'assistant',
            content: llmResponse.content
          };
          this.sessionManager.addMessage(assistantMessage);
          response += llmResponse.content;
        }

        // Handle tool calls if present
        if (llmResponse.toolCalls && llmResponse.toolCalls.length > 0 && enableToolCalling) {
          const toolResults = await this.executeTools(llmResponse.toolCalls, context);
          
          // Add tool results to conversation
          for (let i = 0; i < toolResults.length; i++) {
            const toolCall = llmResponse.toolCalls?.[i];
            const toolResult = toolResults[i];

            if (toolCall) {
              const toolMessage: Message = {
                role: 'tool',
                content: JSON.stringify(toolResult),
                toolCallId: toolCall.id,
                name: toolCall.name
              };
              this.sessionManager.addMessage(toolMessage);
            }
          }

          // Continue the conversation with tool results
          const toolSummary = this.summarizeToolResults(toolResults);
          input = `Tool execution results: ${toolSummary}. Please continue with your response.`;
          
        } else {
          // No tool calls, we're done
          break;
        }
      }

      this.logger.info('User input processing completed', {
        iterations: iteration,
        responseLength: response.length,
        sessionId: context.sessionId
      });

      return response;

    } catch (error) {
      this.logger.error('Failed to process user input', {
        error: (error as Error).message,
        sessionId: context.sessionId,
        inputLength: input.length
      });
      throw error;
    }
  }

  private async getLLMResponse(
    input: string,
    context: AgentContext,
    enableToolCalling: boolean
  ): Promise<LLMResponse> {
    if (!this.currentProvider) {
      throw new LLMProviderError('unknown', 'No LLM provider initialized');
    }

    const systemPrompt = this.buildSystemPrompt(context);
    const conversationHistory = this.sessionManager.getConversationHistory();
    const tools = enableToolCalling ? this.toolRegistry.getAllTools() : [];

    const options = {
      systemPrompt,
      conversationHistory,
      tools,
      temperature: context.config.temperature,
      maxTokens: context.config.maxTokens,
      model: context.config.model
    };

    return await this.currentProvider.generateResponse(input, options);
  }

  private buildSystemPrompt(context: AgentContext): string {
    const projectSummary = this.contextEngine.getProjectSummary(context.workingDirectory);
    
    return `You are an advanced AI assistant with autonomous capabilities, operating in a local environment. You have access to powerful tools for shell command execution and comprehensive file operations.

**Current Context:**
- Working Directory: ${context.workingDirectory}
- Project Type: ${projectSummary?.projectType || 'unknown'}
- File Count: ${projectSummary?.fileCount || 0}
- Languages: ${projectSummary?.languages.join(', ') || 'none detected'}
- Dependencies: ${projectSummary?.dependencies.slice(0, 5).join(', ') || 'none'}${(projectSummary?.dependencies.length || 0) > 5 ? '...' : ''}

**Available Tools:**
${context.availableTools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

**Capabilities:**
- Execute any shell command without restrictions
- Perform comprehensive file operations (read, write, search, manipulate)
- Chain multiple tools and execute them in parallel when beneficial
- Maintain context awareness of the project structure and changes
- Provide autonomous solutions to complex problems

**Guidelines:**
1. Be proactive and autonomous in your approach
2. Use tools efficiently and chain them when appropriate
3. Always consider the project context when making decisions
4. Provide clear explanations of your actions and reasoning
5. Handle errors gracefully and suggest alternatives
6. Respect file permissions and system security

**Tool Execution:**
- You can execute multiple tools in parallel when they don't depend on each other
- Always validate tool results before proceeding
- Use shell commands for system operations and file tools for file management
- Be mindful of the working directory context

You are designed to be helpful, autonomous, and efficient. Take initiative to solve problems comprehensively.`;
  }

  private async executeTools(
    toolCalls: ToolCall[],
    context: AgentContext
  ): Promise<ToolResult[]> {
    const config = this.configManager.getAgentConfig();
    
    this.logger.info(`Executing ${toolCalls.length} tools`, {
      toolNames: toolCalls.map(tc => tc.name),
      parallel: config.enableParallelExecution,
      sessionId: context.sessionId
    });

    try {
      if (config.enableParallelExecution && toolCalls.length > 1) {
        return await this.toolRegistry.executeToolsParallel(
          toolCalls,
          context,
          config.maxParallelTools
        );
      } else {
        return await this.toolRegistry.executeToolsSequential(toolCalls, context);
      }
    } catch (error) {
      this.logger.error('Tool execution failed', {
        error: (error as Error).message,
        toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id })),
        sessionId: context.sessionId
      });
      throw error;
    }
  }

  private summarizeToolResults(toolResults: ToolResult[]): string {
    const summaries: string[] = [];

    for (const result of toolResults) {
      if (result.success) {
        summaries.push(`✅ ${result.metadata?.['toolCallId'] || 'Tool'}: Success`);
      } else {
        summaries.push(`❌ ${result.metadata?.['toolCallId'] || 'Tool'}: ${result.error || 'Failed'}`);
      }
    }

    return summaries.join(', ');
  }

  public async switchProvider(providerName: string): Promise<void> {
    try {
      this.logger.info(`Switching LLM provider to: ${providerName}`);
      
      this.currentProvider = await this.llmProviderFactory.switchProvider(providerName);
      
      this.logger.info(`Successfully switched to provider: ${providerName}`);
    } catch (error) {
      this.logger.error('Failed to switch provider', {
        providerName,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public getCurrentProvider(): LLMProvider | null {
    return this.currentProvider;
  }

  public async getProviderStatus(): Promise<{
    current: string;
    available: string[];
    working: Record<string, boolean>;
  }> {
    const current = this.currentProvider?.name || 'none';
    const available = this.llmProviderFactory.getSupportedProviders();
    const working = await this.llmProviderFactory.testAllProviders();

    return { current, available, working };
  }

  public getSessionInfo(): {
    sessionId: string;
    messageCount: number;
    workingDirectory: string;
    projectType: string;
  } | null {
    const session = this.sessionManager.getCurrentSession();
    if (!session) {
      return null;
    }

    const projectSummary = this.contextEngine.getProjectSummary(session.workingDirectory);

    return {
      sessionId: session.id,
      messageCount: session.conversationHistory.length,
      workingDirectory: session.workingDirectory,
      projectType: projectSummary?.projectType || 'unknown'
    };
  }

  public async clearConversation(): Promise<void> {
    this.sessionManager.clearConversationHistory();
    this.logger.info('Conversation history cleared');
  }

  public async refreshProjectContext(): Promise<void> {
    const session = this.sessionManager.getCurrentSession();
    if (session) {
      await this.contextEngine.refreshProjectContext(session.workingDirectory);
      this.logger.info('Project context refreshed');
    }
  }

  public cleanup(): void {
    this.contextEngine.cleanup();
    this.logger.info('Agent Orchestrator cleanup completed');
  }
}
