import { Tool, ToolR<PERSON>ult, Agent<PERSON>ontext, ToolCall } from '@/types';
export declare class ToolRegistry {
    private static instance;
    private tools;
    private logger;
    private executionQueue;
    private constructor();
    static getInstance(): ToolRegistry;
    private registerDefaultTools;
    registerTool(tool: Tool): void;
    unregisterTool(toolName: string): boolean;
    getTool(toolName: string): Tool | undefined;
    getAllTools(): Tool[];
    getToolNames(): string[];
    hasToolCalling(): boolean;
    executeTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult>;
    executeToolsParallel(toolCalls: ToolCall[], context: AgentContext, maxParallel?: number): Promise<ToolResult[]>;
    executeToolsSequential(toolCalls: ToolCall[], context: AgentContext): Promise<ToolResult[]>;
    private isCriticalTool;
    getToolSchema(toolName: string): any;
    getAllToolSchemas(): any[];
    validateToolCall(toolCall: ToolCall): boolean;
    getToolUsageStats(): Record<string, {
        calls: number;
        successes: number;
        failures: number;
    }>;
}
//# sourceMappingURL=ToolRegistry.d.ts.map