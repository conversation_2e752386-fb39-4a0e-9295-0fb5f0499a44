import { AgentConfig } from '@/types';
export interface AppConfig {
    agent: AgentConfig;
    providers: {
        openai?: {
            apiKey?: string;
            baseURL?: string;
            organization?: string;
        };
        anthropic?: {
            apiKey?: string;
            baseURL?: string;
        };
        deepseek?: {
            apiKey?: string;
            baseURL?: string;
        };
        gemini?: {
            apiKey?: string;
        };
        mistral?: {
            apiKey?: string;
            baseURL?: string;
        };
        ollama?: {
            baseURL?: string;
            timeout?: number;
        };
    };
    session: {
        persistenceEnabled: boolean;
        maxSessions: number;
        sessionTimeout: number;
        autoCleanup: boolean;
    };
    context: {
        indexingEnabled: boolean;
        maxFileSize: number;
        excludePatterns: string[];
        includePatterns: string[];
        watchForChanges: boolean;
    };
    logging: {
        level: string;
        enableFileLogging: boolean;
        maxLogFiles: number;
        maxLogSize: string;
    };
}
export declare class ConfigManager {
    private static instance;
    private config;
    private configPath;
    private logger;
    private constructor();
    static getInstance(): ConfigManager;
    private getDefaultConfig;
    private loadConfig;
    private mergeConfigs;
    private saveConfig;
    getConfig(): AppConfig;
    getAgentConfig(): AgentConfig;
    getProviderConfig(provider: string): any;
    updateConfig(updates: Partial<AppConfig>): void;
    updateAgentConfig(updates: Partial<AgentConfig>): void;
    setProviderApiKey(provider: string, apiKey: string): void;
    validateConfig(): boolean;
    getConfigPath(): string;
}
//# sourceMappingURL=ConfigManager.d.ts.map