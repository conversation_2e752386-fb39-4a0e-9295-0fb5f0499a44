{"version": 3, "file": "ShellTool.js", "sourceRoot": "", "sources": ["../../src/tools/ShellTool.ts"], "names": [], "mappings": ";;;AAAA,iDAA4C;AAC5C,+BAAiC;AAEjC,uDAA2D;AAC3D,2CAAwC;AAExC,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,oBAAI,CAAC,CAAC;AAElC,MAAa,SAAS;IACJ,IAAI,GAAG,OAAO,CAAC;IACf,WAAW,GAAG,gDAAgD,CAAC;IAC/D,UAAU,GAAG;QAC3B,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,8BAA8B;aAC5C;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,8CAA8C;aAC5D;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,kCAAkC;aAChD;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oDAAoD;aAClE;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,kDAAkD;aAChE;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,oDAAoD;aAClE;SACF;QACD,QAAQ,EAAE,CAAC,SAAS,CAAC;KACtB,CAAC;IAEM,MAAM,CAAS;IAEvB;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAS,EAAE,OAAqB;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,OAAO,GAA0B;gBACrC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,gBAAgB;gBACzC,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,KAAK;gBAC9B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE,OAAO;gBACpC,UAAU,EAAE,SAAS;aACtB,CAAC;YAEF,IAAI,MAA4B,CAAC;YAEjC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE3B,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEhE,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE;oBACR,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,QAAQ,EAAE,QAAQ;oBAClB,GAAG,EAAE,OAAO,CAAC,GAAG;iBACjB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,IAAI,CAAC,OAAO,EAAE,EAAE;gBACnE,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,QAAQ;aACT,CAAC,CAAC;YAEH,IAAI,KAAK,YAAY,kCAAmB,EAAE,CAAC;gBACzC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,kCAAmB,CAC3B,IAAI,CAAC,OAAO,EACZ,CAAC,CAAC,EACD,KAAe,CAAC,OAAO,EACxB,EAAE,QAAQ,EAAE,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,OAAe,EACf,OAA8B;QAE9B,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE;gBAClD,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,OAAO,CAAC,QAAe;gBACjC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,UAAU,EAAE,OAAO,CAAC,UAAiB;aACtC,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE;gBACzB,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE;gBACzB,QAAQ,EAAE,CAAC;gBACX,OAAO;gBACP,QAAQ,EAAE,CAAC,EAAE,wBAAwB;gBACrC,OAAO,EAAE,IAAI;aACd,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;YAClC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC;YACzD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YAE9C,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;gBACnB,MAAM,IAAI,kCAAmB,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO;gBACL,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,OAAO;gBACP,QAAQ,EAAE,CAAC,EAAE,wBAAwB;gBACrC,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,OAAe,EACf,OAA8B;QAE9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,EAAG,CAAC;YAE1B,MAAM,KAAK,GAAG,IAAA,qBAAK,EAAC,GAAG,EAAE,IAAI,EAAE;gBAC7B,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAChC,CAAC,CAAC;YAEH,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAChC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC/B,MAAM,IAAI,MAAM,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAChC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC/B,MAAM,IAAI,MAAM,CAAC;gBACjB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBACzB,MAAM,MAAM,GAAyB;oBACnC,MAAM;oBACN,MAAM;oBACN,QAAQ,EAAE,IAAI,IAAI,CAAC;oBACnB,OAAO;oBACP,QAAQ,EAAE,CAAC,EAAE,wBAAwB;oBACrC,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC;iBAC3B,CAAC;gBAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,kCAAmB,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,MAAM,CAAC,IAAI,kCAAmB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;YAEH,cAAc;YACd,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,UAAU,CAAC,GAAG,EAAE;oBACd,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,UAAiB,CAAC,CAAC;oBACtC,MAAM,CAAC,IAAI,kCAAmB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC;gBACpE,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8CAA8C;IACvC,KAAK,CAAC,aAAa,CAAC,IAAY,EAAE,OAAqB;QAC5D,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;QAC/C,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,WAAW,IAAI,GAAG,CAAC;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,OAAqB;QACpD,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;QAC/C,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,WAAmB,EAAE,OAAqB;QACxE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;YAC/C,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC;YAC5E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;YACxD,OAAO,MAAM,CAAC,OAAO,CAAC;QACxB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,OAAqB;QAC9C,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;QAC/C,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;CACF;AAzOD,8BAyOC"}