"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShellTool = void 0;
const child_process_1 = require("child_process");
const util_1 = require("util");
const ErrorHandler_1 = require("@/utils/ErrorHandler");
const Logger_1 = require("@/utils/Logger");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class ShellTool {
    name = 'shell';
    description = 'Execute shell commands with full system access';
    parameters = {
        type: 'object',
        properties: {
            command: {
                type: 'string',
                description: 'The shell command to execute'
            },
            cwd: {
                type: 'string',
                description: 'Working directory for the command (optional)'
            },
            env: {
                type: 'object',
                description: 'Environment variables (optional)'
            },
            timeout: {
                type: 'number',
                description: 'Timeout in milliseconds (optional, default: 30000)'
            },
            shell: {
                type: 'string',
                description: 'Shell to use (optional, default: system default)'
            },
            interactive: {
                type: 'boolean',
                description: 'Run in interactive mode (optional, default: false)'
            }
        },
        required: ['command']
    };
    logger;
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
    }
    async execute(args, context) {
        const startTime = Date.now();
        try {
            const options = {
                cwd: args.cwd || context.workingDirectory,
                env: { ...process.env, ...args.env },
                timeout: args.timeout || 30000,
                shell: args.shell,
                encoding: 'utf8',
                maxBuffer: 1024 * 1024 * 10, // 10MB
                killSignal: 'SIGTERM'
            };
            let result;
            if (args.interactive) {
                result = await this.executeInteractive(args.command, options);
            }
            else {
                result = await this.executeNonInteractive(args.command, options);
            }
            const duration = Date.now() - startTime;
            result.duration = duration;
            this.logger.logToolExecution(this.name, args, result, duration);
            return {
                success: result.success,
                result: result,
                metadata: {
                    command: args.command,
                    exitCode: result.exitCode,
                    duration: duration,
                    cwd: options.cwd
                }
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`Shell command execution failed: ${args.command}`, {
                error: error.message,
                command: args.command,
                duration
            });
            if (error instanceof ErrorHandler_1.ShellExecutionError) {
                throw error;
            }
            throw new ErrorHandler_1.ShellExecutionError(args.command, -1, error.message, { duration });
        }
    }
    async executeNonInteractive(command, options) {
        try {
            const { stdout, stderr } = await execAsync(command, {
                cwd: options.cwd,
                env: options.env,
                timeout: options.timeout,
                shell: options.shell,
                encoding: options.encoding,
                maxBuffer: options.maxBuffer,
                killSignal: options.killSignal
            });
            return {
                stdout: stdout.toString(),
                stderr: stderr.toString(),
                exitCode: 0,
                command,
                duration: 0, // Will be set by caller
                success: true
            };
        }
        catch (error) {
            const exitCode = error.code || -1;
            const stderr = error.stderr?.toString() || error.message;
            const stdout = error.stdout?.toString() || '';
            if (exitCode !== 0) {
                throw new ErrorHandler_1.ShellExecutionError(command, exitCode, stderr);
            }
            return {
                stdout,
                stderr,
                exitCode,
                command,
                duration: 0, // Will be set by caller
                success: false
            };
        }
    }
    async executeInteractive(command, options) {
        return new Promise((resolve, reject) => {
            const args = command.split(' ');
            const cmd = args.shift();
            const child = (0, child_process_1.spawn)(cmd, args, {
                cwd: options.cwd,
                env: options.env,
                shell: options.shell,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            let stdout = '';
            let stderr = '';
            child.stdout?.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                console.log(output);
            });
            child.stderr?.on('data', (data) => {
                const output = data.toString();
                stderr += output;
                console.error(output);
            });
            child.on('close', (code) => {
                const result = {
                    stdout,
                    stderr,
                    exitCode: code || 0,
                    command,
                    duration: 0, // Will be set by caller
                    success: (code || 0) === 0
                };
                if (result.success) {
                    resolve(result);
                }
                else {
                    reject(new ErrorHandler_1.ShellExecutionError(command, result.exitCode, stderr));
                }
            });
            child.on('error', (error) => {
                reject(new ErrorHandler_1.ShellExecutionError(command, -1, error.message));
            });
            // Set timeout
            if (options.timeout) {
                setTimeout(() => {
                    child.kill(options.killSignal);
                    reject(new ErrorHandler_1.ShellExecutionError(command, -1, 'Command timed out'));
                }, options.timeout);
            }
        });
    }
    // Utility methods for common shell operations
    async listDirectory(path, context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ? `dir "${path}"` : `ls -la "${path}"`;
        return this.execute({ command }, context);
    }
    async getCurrentDirectory(context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ? 'cd' : 'pwd';
        return this.execute({ command }, context);
    }
    async checkCommandExists(commandName, context) {
        try {
            const isWindows = process.platform === 'win32';
            const command = isWindows ? `where ${commandName}` : `which ${commandName}`;
            const result = await this.execute({ command }, context);
            return result.success;
        }
        catch {
            return false;
        }
    }
    async getSystemInfo(context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ? 'systeminfo' : 'uname -a';
        return this.execute({ command }, context);
    }
    async executeWithPipe(commands, context) {
        const isWindows = process.platform === 'win32';
        const pipeOperator = isWindows ? '|' : '|';
        const combinedCommand = commands.join(` ${pipeOperator} `);
        return this.execute({ command: combinedCommand }, context);
    }
    async executeInBackground(command, context) {
        const isWindows = process.platform === 'win32';
        const backgroundCommand = isWindows ? `start /B ${command}` : `${command} &`;
        return this.execute({ command: backgroundCommand }, context);
    }
    async listProcesses(context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ? 'tasklist' : 'ps aux';
        return this.execute({ command }, context);
    }
    async killProcess(pid, context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ? `taskkill /PID ${pid} /F` : `kill -9 ${pid}`;
        return this.execute({ command }, context);
    }
    async findProcess(processName, context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ?
            `tasklist /FI "IMAGENAME eq ${processName}"` :
            `ps aux | grep ${processName}`;
        return this.execute({ command }, context);
    }
    async getEnvironmentVariables(context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ? 'set' : 'env';
        return this.execute({ command }, context);
    }
    async getDiskUsage(path, context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ? `dir "${path}" /-c` : `du -sh "${path}"`;
        return this.execute({ command }, context);
    }
    async getNetworkInfo(context) {
        const isWindows = process.platform === 'win32';
        const command = isWindows ? 'ipconfig /all' : 'ifconfig -a';
        return this.execute({ command }, context);
    }
    async monitorCommand(command, context, intervalMs = 5000) {
        // This is a simplified monitoring implementation
        // In a real scenario, you'd set up proper monitoring with intervals
        const monitoringCommand = `echo "Monitoring: ${command}" && ${command}`;
        return this.execute({
            command: monitoringCommand,
            timeout: intervalMs * 2
        }, context);
    }
    async executeWithRetry(command, context, maxRetries = 3, retryDelay = 1000) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const result = await this.execute({ command }, context);
                if (result.success) {
                    return result;
                }
                if (attempt === maxRetries) {
                    return result;
                }
                this.logger.warn(`Shell command failed, retrying (${attempt}/${maxRetries}): ${command}`, {
                    error: result.result?.stderr || 'Unknown error',
                    attempt
                });
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
            }
            catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw error;
                }
                this.logger.warn(`Shell command error, retrying (${attempt}/${maxRetries}): ${command}`, {
                    error: error.message,
                    attempt
                });
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
            }
        }
        throw lastError || new ErrorHandler_1.ShellExecutionError(command, -1, 'Max retries exceeded');
    }
    async executeBatch(commands, context, stopOnFailure = false) {
        const results = [];
        for (const command of commands) {
            try {
                const result = await this.execute({ command }, context);
                results.push(result);
                if (!result.success && stopOnFailure) {
                    this.logger.warn(`Batch execution stopped due to failure: ${command}`);
                    break;
                }
            }
            catch (error) {
                const failedResult = {
                    success: false,
                    result: null,
                    error: error.message,
                    metadata: { command }
                };
                results.push(failedResult);
                if (stopOnFailure) {
                    this.logger.warn(`Batch execution stopped due to error: ${command}`);
                    break;
                }
            }
        }
        return results;
    }
}
exports.ShellTool = ShellTool;
//# sourceMappingURL=ShellTool.js.map