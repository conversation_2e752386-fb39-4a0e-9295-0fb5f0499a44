import fs from 'fs-extra';
import path from 'path';
import glob from 'fast-glob';
import { Tool, ToolR<PERSON>ult, AgentContext, FileOperationOptions, FileOperationResult, SearchOptions, SearchResult } from '@/types';
import { FileOperationError } from '@/utils/ErrorHandler';
import { Logger } from '@/utils/Logger';

export class FileTool implements Tool {
  public readonly name = 'file';
  public readonly description = 'Comprehensive file operations including read, write, search, and manipulation';
  public readonly parameters = {
    type: 'object' as const,
    properties: {
      operation: {
        type: 'string',
        description: 'File operation to perform',
        enum: ['read', 'write', 'append', 'delete', 'copy', 'move', 'mkdir', 'rmdir', 'exists', 'stat', 'search', 'glob', 'permissions']
      },
      path: {
        type: 'string',
        description: 'File or directory path'
      },
      content: {
        type: 'string',
        description: 'Content to write (for write/append operations)'
      },
      destination: {
        type: 'string',
        description: 'Destination path (for copy/move operations)'
      },
      pattern: {
        type: 'string',
        description: 'Search pattern or glob pattern'
      },
      options: {
        type: 'object',
        description: 'Additional options for the operation'
      }
    },
    required: ['operation', 'path']
  };

  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance();
  }

  public async execute(args: any, context: AgentContext): Promise<ToolResult> {
    const startTime = Date.now();
    
    try {
      const absolutePath = path.resolve(context.workingDirectory, args.path);
      const options: FileOperationOptions = args.options || {};

      let result: FileOperationResult;

      switch (args.operation) {
        case 'read':
          result = await this.readFile(absolutePath, options);
          break;
        case 'write':
          result = await this.writeFile(absolutePath, args.content, options);
          break;
        case 'append':
          result = await this.appendFile(absolutePath, args.content, options);
          break;
        case 'delete':
          result = await this.deleteFile(absolutePath, options);
          break;
        case 'copy':
          result = await this.copyFile(absolutePath, args.destination, options);
          break;
        case 'move':
          result = await this.moveFile(absolutePath, args.destination, options);
          break;
        case 'mkdir':
          result = await this.createDirectory(absolutePath, options);
          break;
        case 'rmdir':
          result = await this.removeDirectory(absolutePath, options);
          break;
        case 'exists':
          result = await this.checkExists(absolutePath);
          break;
        case 'stat':
          result = await this.getStats(absolutePath);
          break;
        case 'search':
          const searchOptions: SearchOptions = { pattern: args.pattern, ...options };
          const searchResults = await this.searchInFiles(absolutePath, searchOptions);
          result = {
            success: true,
            path: absolutePath,
            operation: 'search',
            metadata: { results: searchResults, count: searchResults.length }
          };
          break;
        case 'glob':
          const globResults = await this.globFiles(args.pattern, context.workingDirectory);
          result = {
            success: true,
            path: context.workingDirectory,
            operation: 'glob',
            metadata: { files: globResults, count: globResults.length }
          };
          break;
        case 'permissions':
          result = await this.getPermissions(absolutePath);
          break;
        default:
          throw new FileOperationError(args.operation, absolutePath, 'Unknown operation');
      }

      const duration = Date.now() - startTime;
      this.logger.logToolExecution(this.name, args, result, duration);

      return {
        success: result.success,
        result: result,
        metadata: {
          operation: args.operation,
          path: absolutePath,
          duration
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`File operation failed: ${args.operation} on ${args.path}`, {
        error: (error as Error).message,
        operation: args.operation,
        path: args.path,
        duration
      });

      if (error instanceof FileOperationError) {
        throw error;
      }

      throw new FileOperationError(
        args.operation,
        args.path,
        (error as Error).message,
        { duration }
      );
    }
  }

  private async readFile(filePath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const content = await fs.readFile(filePath, options.encoding || 'utf8');
      const stats = await fs.stat(filePath);
      
      return {
        success: true,
        path: filePath,
        operation: 'read',
        size: stats.size,
        metadata: { content, encoding: options.encoding || 'utf8' }
      };
    } catch (error) {
      throw new FileOperationError('read', filePath, (error as Error).message);
    }
  }

  private async writeFile(filePath: string, content: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.createDirectories) {
        await fs.ensureDir(path.dirname(filePath));
      }

      if (!options.overwrite && await fs.pathExists(filePath)) {
        throw new Error('File exists and overwrite is disabled');
      }

      await fs.writeFile(filePath, content, options.encoding || 'utf8');
      const stats = await fs.stat(filePath);

      return {
        success: true,
        path: filePath,
        operation: 'write',
        size: stats.size
      };
    } catch (error) {
      throw new FileOperationError('write', filePath, (error as Error).message);
    }
  }

  private async appendFile(filePath: string, content: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.createDirectories) {
        await fs.ensureDir(path.dirname(filePath));
      }

      await fs.appendFile(filePath, content, options.encoding || 'utf8');
      const stats = await fs.stat(filePath);

      return {
        success: true,
        path: filePath,
        operation: 'append',
        size: stats.size
      };
    } catch (error) {
      throw new FileOperationError('append', filePath, (error as Error).message);
    }
  }

  private async deleteFile(filePath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.force) {
        await fs.remove(filePath);
      } else {
        await fs.unlink(filePath);
      }

      return {
        success: true,
        path: filePath,
        operation: 'delete'
      };
    } catch (error) {
      throw new FileOperationError('delete', filePath, (error as Error).message);
    }
  }

  private async copyFile(sourcePath: string, destPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const absoluteDestPath = path.resolve(path.dirname(sourcePath), destPath);
      
      if (options.createDirectories) {
        await fs.ensureDir(path.dirname(absoluteDestPath));
      }

      const copyOptions: any = {};
      if (options.overwrite !== undefined) {
        copyOptions.overwrite = options.overwrite;
      }
      if (options.preserveTimestamps) {
        copyOptions.preserveTimestamps = true;
      }

      await fs.copy(sourcePath, absoluteDestPath, copyOptions);
      const stats = await fs.stat(absoluteDestPath);

      return {
        success: true,
        path: absoluteDestPath,
        operation: 'copy',
        size: stats.size,
        metadata: { source: sourcePath, destination: absoluteDestPath }
      };
    } catch (error) {
      throw new FileOperationError('copy', sourcePath, (error as Error).message);
    }
  }

  private async moveFile(sourcePath: string, destPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const absoluteDestPath = path.resolve(path.dirname(sourcePath), destPath);
      
      if (options.createDirectories) {
        await fs.ensureDir(path.dirname(absoluteDestPath));
      }

      await fs.move(sourcePath, absoluteDestPath, { overwrite: options.overwrite });
      const stats = await fs.stat(absoluteDestPath);

      return {
        success: true,
        path: absoluteDestPath,
        operation: 'move',
        size: stats.size,
        metadata: { source: sourcePath, destination: absoluteDestPath }
      };
    } catch (error) {
      throw new FileOperationError('move', sourcePath, (error as Error).message);
    }
  }

  private async createDirectory(dirPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.recursive !== false) {
        await fs.ensureDir(dirPath);
      } else {
        await fs.mkdir(dirPath);
      }

      return {
        success: true,
        path: dirPath,
        operation: 'mkdir'
      };
    } catch (error) {
      throw new FileOperationError('mkdir', dirPath, (error as Error).message);
    }
  }

  private async removeDirectory(dirPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.recursive !== false) {
        await fs.remove(dirPath);
      } else {
        await fs.rmdir(dirPath);
      }

      return {
        success: true,
        path: dirPath,
        operation: 'rmdir'
      };
    } catch (error) {
      throw new FileOperationError('rmdir', dirPath, (error as Error).message);
    }
  }

  private async checkExists(filePath: string): Promise<FileOperationResult> {
    try {
      const exists = await fs.pathExists(filePath);
      
      return {
        success: true,
        path: filePath,
        operation: 'exists',
        metadata: { exists }
      };
    } catch (error) {
      throw new FileOperationError('exists', filePath, (error as Error).message);
    }
  }

  private async getStats(filePath: string): Promise<FileOperationResult> {
    try {
      const stats = await fs.stat(filePath);
      
      return {
        success: true,
        path: filePath,
        operation: 'stat',
        size: stats.size,
        metadata: {
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          accessed: stats.atime,
          mode: stats.mode,
          uid: stats.uid,
          gid: stats.gid
        }
      };
    } catch (error) {
      throw new FileOperationError('stat', filePath, (error as Error).message);
    }
  }

  private async getPermissions(filePath: string): Promise<FileOperationResult> {
    try {
      const stats = await fs.stat(filePath);
      const mode = stats.mode;
      const permissions = {
        owner: {
          read: !!(mode & parseInt('400', 8)),
          write: !!(mode & parseInt('200', 8)),
          execute: !!(mode & parseInt('100', 8))
        },
        group: {
          read: !!(mode & parseInt('040', 8)),
          write: !!(mode & parseInt('020', 8)),
          execute: !!(mode & parseInt('010', 8))
        },
        others: {
          read: !!(mode & parseInt('004', 8)),
          write: !!(mode & parseInt('002', 8)),
          execute: !!(mode & parseInt('001', 8))
        }
      };

      return {
        success: true,
        path: filePath,
        operation: 'permissions',
        metadata: { permissions, mode: mode.toString(8) }
      };
    } catch (error) {
      throw new FileOperationError('permissions', filePath, (error as Error).message);
    }
  }

  private async searchInFiles(searchPath: string, options: SearchOptions): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    const files = await glob(options.includeFiles || ['**/*'], {
      cwd: searchPath,
      ignore: options.excludeFiles || [],
      onlyFiles: true,
      absolute: true
    });

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const lines = content.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          if (!line) continue;

          let match: RegExpMatchArray | null = null;

          if (options.regex) {
            const flags = options.caseSensitive ? 'g' : 'gi';
            const regex = new RegExp(options.pattern, flags);
            match = line.match(regex);
          } else {
            const searchText = options.caseSensitive ? line : line.toLowerCase();
            const pattern = options.caseSensitive ? options.pattern : options.pattern.toLowerCase();

            if (options.wholeWord) {
              const regex = new RegExp(`\\b${pattern}\\b`, options.caseSensitive ? 'g' : 'gi');
              match = line.match(regex);
            } else {
              if (searchText && searchText.includes(pattern)) {
                match = [pattern];
              }
            }
          }

          if (match) {
            const contextStart = Math.max(0, i - (options.context || 0));
            const contextEnd = Math.min(lines.length - 1, i + (options.context || 0));
            const context = lines.slice(contextStart, contextEnd + 1);

            results.push({
              file,
              line: i + 1,
              column: line.indexOf(match[0]) + 1,
              match: match[0],
              context
            });

            if (options.maxResults && results.length >= options.maxResults) {
              return results;
            }
          }
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    return results;
  }

  private async globFiles(pattern: string, cwd: string): Promise<string[]> {
    try {
      return await glob(pattern, { cwd, absolute: true });
    } catch (error) {
      throw new Error(`Glob pattern failed: ${(error as Error).message}`);
    }
  }
}
