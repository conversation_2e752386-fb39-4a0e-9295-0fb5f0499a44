"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
class Logger {
    static instance;
    logger;
    sessionId;
    constructor() {
        this.initializeLogger();
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    initializeLogger() {
        const logsDir = path_1.default.join(process.cwd(), '.ai-cli', 'logs');
        fs_extra_1.default.ensureDirSync(logsDir);
        const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, sessionId, ...meta }) => {
            const logEntry = {
                level: level,
                message,
                timestamp: new Date(timestamp),
                sessionId,
                metadata: Object.keys(meta).length > 0 ? meta : undefined
            };
            return JSON.stringify(logEntry);
        }));
        this.logger = winston_1.default.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: logFormat,
            transports: [
                new winston_1.default.transports.File({
                    filename: path_1.default.join(logsDir, 'error.log'),
                    level: 'error',
                    maxsize: 5242880, // 5MB
                    maxFiles: 5
                }),
                new winston_1.default.transports.File({
                    filename: path_1.default.join(logsDir, 'combined.log'),
                    maxsize: 5242880, // 5MB
                    maxFiles: 10
                }),
                new winston_1.default.transports.Console({
                    format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple(), winston_1.default.format.printf(({ level, message, timestamp }) => {
                        return `${timestamp} [${level}]: ${message}`;
                    }))
                })
            ]
        });
    }
    setSessionId(sessionId) {
        this.sessionId = sessionId;
    }
    error(message, metadata) {
        this.logger.error(message, { sessionId: this.sessionId, ...metadata });
    }
    warn(message, metadata) {
        this.logger.warn(message, { sessionId: this.sessionId, ...metadata });
    }
    info(message, metadata) {
        this.logger.info(message, { sessionId: this.sessionId, ...metadata });
    }
    debug(message, metadata) {
        this.logger.debug(message, { sessionId: this.sessionId, ...metadata });
    }
    verbose(message, metadata) {
        this.logger.verbose(message, { sessionId: this.sessionId, ...metadata });
    }
    logToolExecution(toolName, args, result, duration) {
        this.info(`Tool executed: ${toolName}`, {
            tool: toolName,
            arguments: args,
            result: result,
            duration,
            type: 'tool_execution'
        });
    }
    logLLMCall(provider, model, prompt, response, usage) {
        this.info(`LLM call: ${provider}/${model}`, {
            provider,
            model,
            promptLength: prompt.length,
            responseLength: response.length,
            usage,
            type: 'llm_call'
        });
    }
    logSessionEvent(event, metadata) {
        this.info(`Session event: ${event}`, {
            event,
            sessionId: this.sessionId,
            type: 'session_event',
            ...metadata
        });
    }
    logError(error, context) {
        this.error(`${context ? `${context}: ` : ''}${error.message}`, {
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack
            },
            context,
            type: 'error'
        });
    }
}
exports.Logger = Logger;
//# sourceMappingURL=Logger.js.map