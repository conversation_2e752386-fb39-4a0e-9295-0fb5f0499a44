import { GoogleGenerativeAI } from '@google/generative-ai';
import { <PERSON><PERSON><PERSON>ider, LLMO<PERSON>s, LLMResponse, ToolCall, <PERSON>l<PERSON><PERSON>ult, AgentContext } from '@/types';
import { LLMProviderError } from '@/utils/ErrorHandler';
import { Logger } from '@/utils/Logger';
import { ToolRegistry } from '@/tools/ToolRegistry';

export class GeminiProvider implements LLMProvider {
  public readonly name = 'gemini';
  private client: GoogleGenerativeAI;
  private logger: Logger;
  private toolRegistry: ToolRegistry;

  constructor(config: any) {
    this.logger = Logger.getInstance();
    this.toolRegistry = ToolRegistry.getInstance();

    if (!config.apiKey) {
      throw new LLMProviderError(this.name, 'Google Gemini API key is required');
    }

    this.client = new GoogleGenerativeAI(config.apiKey);
  }

  public async generateResponse(prompt: string, options: LLMOptions = {}): Promise<LLMResponse> {
    try {
      const model = options.model || 'gemini-pro';
      const genModel = this.client.getGenerativeModel({ model });

      const generationConfig = {
        temperature: options.temperature || 0.7,
        maxOutputTokens: options.maxTokens || 4000
      };

      // Build the prompt with tool descriptions if tools are available
      let enhancedPrompt = prompt;
      if (options.tools && options.tools.length > 0) {
        const toolDescriptions = this.buildToolDescriptions(options.tools);
        enhancedPrompt = `${options.systemPrompt || ''}\n\n${toolDescriptions}\n\nUser: ${prompt}`;
      } else if (options.systemPrompt) {
        enhancedPrompt = `${options.systemPrompt}\n\nUser: ${prompt}`;
      }

      this.logger.debug('Making Gemini API request', {
        model,
        prompt: prompt.substring(0, 100) + '...',
        hasTools: !!(options.tools && options.tools.length > 0)
      });

      const result = await genModel.generateContent({
        contents: [{ role: 'user', parts: [{ text: enhancedPrompt }] }],
        generationConfig
      });

      const response = result.response;
      const content = response.text() || '';

      // Parse tool calls from response if tools were provided
      const toolCalls = options.tools && options.tools.length > 0 ?
        this.parseToolCalls(content) : [];

      const llmResponse: LLMResponse = {
        content,
        toolCalls,
        usage: response.usageMetadata ? {
          promptTokens: response.usageMetadata.promptTokenCount || 0,
          completionTokens: response.usageMetadata.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata.totalTokenCount || 0
        } : undefined
      };

      this.logger.logLLMCall(this.name, model, prompt, content, llmResponse.usage);

      return llmResponse;

    } catch (error) {
      this.logger.error('Gemini API request failed', {
        error: (error as Error).message,
        prompt: prompt.substring(0, 100) + '...'
      });

      throw new LLMProviderError(this.name, (error as Error).message);
    }
  }

  public async *generateStreamResponse(prompt: string, options: LLMOptions = {}): AsyncGenerator<string, void, unknown> {
    try {
      const model = options.model || 'gemini-pro';
      const genModel = this.client.getGenerativeModel({ model });

      const generationConfig = {
        temperature: options.temperature || 0.7,
        maxOutputTokens: options.maxTokens || 4000
      };

      this.logger.debug('Making Gemini streaming API request', {
        model,
        prompt: prompt.substring(0, 100) + '...'
      });

      const result = await genModel.generateContentStream({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig
      });

      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        if (chunkText) {
          yield chunkText;
        }
      }

    } catch (error) {
      this.logger.error('Gemini streaming API request failed', {
        error: (error as Error).message,
        prompt: prompt.substring(0, 100) + '...'
      });

      throw new LLMProviderError(this.name, (error as Error).message);
    }
  }

  public supportsToolCalling(): boolean {
    return true; // Now supports tool calling through function descriptions
  }

  public async callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult> {
    try {
      return await this.toolRegistry.executeTool(toolCall, context);
    } catch (error) {
      this.logger.error('Tool execution failed in Gemini provider', {
        toolName: toolCall.name,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public async validateApiKey(): Promise<boolean> {
    try {
      const model = this.client.getGenerativeModel({ model: 'gemini-pro' });
      await model.generateContent('Hello');
      return true;
    } catch (error) {
      this.logger.error('Gemini API key validation failed', {
        error: (error as Error).message
      });
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    // Gemini doesn't have a models endpoint, so return known models
    return ['gemini-pro', 'gemini-pro-vision'];
  }

  public getDefaultModel(): string {
    return 'gemini-pro';
  }

  public getMaxTokens(model?: string): number {
    const tokenLimits: Record<string, number> = {
      'gemini-pro': 32000,
      'gemini-pro-vision': 32000
    };

    return tokenLimits[model || 'gemini-pro'] || 32000;
  }

  public estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  public calculateCost(usage: { promptTokens: number; completionTokens: number }, model?: string): number {
    // Gemini pricing (in USD per 1M tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'gemini-pro': { input: 0.5, output: 1.5 },
      'gemini-pro-vision': { input: 0.5, output: 1.5 }
    };

    const modelPricing = pricing[model || 'gemini-pro'] || pricing['gemini-pro'];
    const inputCost = (usage.promptTokens / 1000000) * modelPricing.input;
    const outputCost = (usage.completionTokens / 1000000) * modelPricing.output;

    return inputCost + outputCost;
  }

  private buildToolDescriptions(tools: any[]): string {
    const descriptions = tools.map(tool => {
      const params = tool.parameters.properties ?
        Object.entries(tool.parameters.properties)
          .map(([name, prop]: [string, any]) => `${name}: ${prop.description}`)
          .join(', ') : '';

      return `${tool.name}(${params}): ${tool.description}`;
    });

    return `Available tools:
${descriptions.join('\n')}

To use a tool, respond with a JSON object in this format:
{"tool_call": {"name": "tool_name", "arguments": {"param1": "value1", "param2": "value2"}}}

You can call multiple tools by providing an array:
[{"tool_call": {"name": "tool1", "arguments": {...}}}, {"tool_call": {"name": "tool2", "arguments": {...}}}]`;
  }

  private parseToolCalls(content: string): ToolCall[] {
    const toolCalls: ToolCall[] = [];

    try {
      // Look for JSON objects with tool_call structure
      const jsonRegex = /\{[^{}]*"tool_call"[^{}]*\}/g;
      const arrayRegex = /\[[^\[\]]*"tool_call"[^\[\]]*\]/g;

      let matches = content.match(arrayRegex);
      if (matches) {
        // Handle array format
        for (const match of matches) {
          try {
            const parsed = JSON.parse(match);
            if (Array.isArray(parsed)) {
              for (const item of parsed) {
                if (item.tool_call) {
                  toolCalls.push({
                    id: `gemini_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    name: item.tool_call.name,
                    arguments: item.tool_call.arguments || {}
                  });
                }
              }
            }
          } catch (e) {
            // Skip invalid JSON
          }
        }
      } else {
        // Handle single object format
        matches = content.match(jsonRegex);
        if (matches) {
          for (const match of matches) {
            try {
              const parsed = JSON.parse(match);
              if (parsed.tool_call) {
                toolCalls.push({
                  id: `gemini_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                  name: parsed.tool_call.name,
                  arguments: parsed.tool_call.arguments || {}
                });
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      this.logger.debug('Failed to parse tool calls from Gemini response', {
        error: (error as Error).message,
        content: content.substring(0, 200)
      });
    }

    return toolCalls;
  }
}
