import { ProjectContext, AgentContext, FileInfo } from '@/types';
export declare class ContextEngine {
    private static instance;
    private projectContexts;
    private fileWatchers;
    private logger;
    private configManager;
    private projectIndexer;
    private toolRegistry;
    private constructor();
    static getInstance(): ContextEngine;
    createAgentContext(sessionId: string, workingDirectory: string): Promise<AgentContext>;
    private getOrCreateProjectContext;
    private shouldRefreshContext;
    private setupFileWatching;
    private handleFileChange;
    getProjectContext(workingDirectory: string): ProjectContext | undefined;
    refreshProjectContext(workingDirectory: string): Promise<ProjectContext>;
    searchFiles(workingDirectory: string, query: string, options?: {
        caseSensitive?: boolean;
        regex?: boolean;
        fileTypes?: string[];
        maxResults?: number;
    }): FileInfo[];
    getFileContent(workingDirectory: string, filePath: string): string | undefined;
    getProjectSummary(workingDirectory: string): {
        projectType: string;
        fileCount: number;
        totalSize: number;
        languages: string[];
        dependencies: string[];
        hasGit: boolean;
    } | undefined;
    cleanup(): void;
}
//# sourceMappingURL=ContextEngine.d.ts.map