"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectIndexer = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const fast_glob_1 = __importDefault(require("fast-glob"));
const Logger_1 = require("@/utils/Logger");
const ConfigManager_1 = require("@/config/ConfigManager");
class ProjectIndexer {
    static instance;
    logger;
    configManager;
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.configManager = ConfigManager_1.ConfigManager.getInstance();
    }
    static getInstance() {
        if (!ProjectIndexer.instance) {
            ProjectIndexer.instance = new ProjectIndexer();
        }
        return ProjectIndexer.instance;
    }
    async indexProject(rootPath) {
        const startTime = Date.now();
        try {
            this.logger.info(`Starting project indexing: ${rootPath}`);
            const [projectType, files, gitInfo, packageInfo] = await Promise.all([
                this.detectProjectType(rootPath),
                this.indexFiles(rootPath),
                this.getGitInfo(rootPath),
                this.getPackageInfo(rootPath)
            ]);
            const dependencies = this.extractDependencies(packageInfo);
            const context = {
                rootPath,
                projectType,
                dependencies,
                files,
                gitInfo,
                packageInfo
            };
            const duration = Date.now() - startTime;
            this.logger.info(`Project indexing completed`, {
                rootPath,
                projectType,
                fileCount: files.length,
                dependencyCount: Object.keys(dependencies).length,
                duration
            });
            return context;
        }
        catch (error) {
            this.logger.error('Project indexing failed', {
                rootPath,
                error: error.message
            });
            throw error;
        }
    }
    async detectProjectType(rootPath) {
        const indicators = [
            { file: 'package.json', type: 'node' },
            { file: 'requirements.txt', type: 'python' },
            { file: 'pyproject.toml', type: 'python' },
            { file: 'Pipfile', type: 'python' },
            { file: 'Cargo.toml', type: 'rust' },
            { file: 'go.mod', type: 'go' },
            { file: 'pom.xml', type: 'java' },
            { file: 'build.gradle', type: 'java' },
            { file: 'composer.json', type: 'php' },
            { file: 'Gemfile', type: 'ruby' },
            { file: '.csproj', type: 'csharp' },
            { file: 'CMakeLists.txt', type: 'cpp' },
            { file: 'Makefile', type: 'c' }
        ];
        for (const indicator of indicators) {
            const filePath = path_1.default.join(rootPath, indicator.file);
            if (await fs_extra_1.default.pathExists(filePath)) {
                return indicator.type;
            }
        }
        // Check for specific file patterns
        const files = await fs_extra_1.default.readdir(rootPath);
        if (files.some(file => file.endsWith('.py')))
            return 'python';
        if (files.some(file => file.endsWith('.js') || file.endsWith('.ts')))
            return 'javascript';
        if (files.some(file => file.endsWith('.rs')))
            return 'rust';
        if (files.some(file => file.endsWith('.go')))
            return 'go';
        if (files.some(file => file.endsWith('.java')))
            return 'java';
        if (files.some(file => file.endsWith('.php')))
            return 'php';
        if (files.some(file => file.endsWith('.rb')))
            return 'ruby';
        if (files.some(file => file.endsWith('.cs')))
            return 'csharp';
        if (files.some(file => file.endsWith('.cpp') || file.endsWith('.c')))
            return 'cpp';
        return 'unknown';
    }
    async indexFiles(rootPath) {
        const config = this.configManager.getConfig().context;
        const files = [];
        try {
            const globPatterns = config.includePatterns.length > 0
                ? config.includePatterns
                : ['**/*'];
            const filePaths = await (0, fast_glob_1.default)(globPatterns, {
                cwd: rootPath,
                ignore: config.excludePatterns,
                onlyFiles: false,
                absolute: true,
                stats: true
            });
            for (const entry of filePaths) {
                try {
                    const filePath = typeof entry === 'string' ? entry : entry.path;
                    const stats = typeof entry === 'string'
                        ? await fs_extra_1.default.stat(filePath)
                        : entry.stats;
                    // Skip files that are too large
                    if (stats.isFile() && stats.size > config.maxFileSize) {
                        continue;
                    }
                    const fileInfo = {
                        path: path_1.default.relative(rootPath, filePath),
                        type: stats.isDirectory() ? 'directory' : 'file',
                        size: stats.size,
                        lastModified: stats.mtime,
                        permissions: stats.mode.toString(8)
                    };
                    // Read content for small text files
                    if (stats.isFile() &&
                        stats.size <= config.maxFileSize &&
                        this.isTextFile(filePath)) {
                        try {
                            fileInfo.content = await fs_extra_1.default.readFile(filePath, 'utf8');
                        }
                        catch (error) {
                            // Skip files that can't be read as text
                        }
                    }
                    files.push(fileInfo);
                }
                catch (error) {
                    this.logger.debug('Failed to index file', {
                        file: typeof entry === 'string' ? entry : entry.path,
                        error: error.message
                    });
                }
            }
        }
        catch (error) {
            this.logger.error('Failed to index files', {
                rootPath,
                error: error.message
            });
        }
        return files;
    }
    isTextFile(filePath) {
        const textExtensions = [
            '.txt', '.md', '.json', '.yaml', '.yml', '.xml', '.html', '.css',
            '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp', '.h',
            '.rs', '.go', '.php', '.rb', '.cs', '.sh', '.bat', '.ps1',
            '.sql', '.r', '.scala', '.kt', '.swift', '.dart', '.lua',
            '.vim', '.ini', '.cfg', '.conf', '.log', '.csv'
        ];
        const ext = path_1.default.extname(filePath).toLowerCase();
        return textExtensions.includes(ext);
    }
    async getGitInfo(rootPath) {
        try {
            const gitDir = path_1.default.join(rootPath, '.git');
            if (!await fs_extra_1.default.pathExists(gitDir)) {
                return undefined;
            }
            // This is a simplified implementation
            // In a real implementation, you'd use a git library like simple-git
            const gitInfo = {
                branch: 'main', // Default
                remotes: [],
                status: 'clean',
                lastCommit: ''
            };
            // Try to read HEAD to get current branch
            try {
                const headPath = path_1.default.join(gitDir, 'HEAD');
                if (await fs_extra_1.default.pathExists(headPath)) {
                    const headContent = await fs_extra_1.default.readFile(headPath, 'utf8');
                    const match = headContent.match(/ref: refs\/heads\/(.+)/);
                    if (match) {
                        gitInfo.branch = match[1].trim();
                    }
                }
            }
            catch (error) {
                // Ignore errors reading git info
            }
            return gitInfo;
        }
        catch (error) {
            this.logger.debug('Failed to get git info', {
                rootPath,
                error: error.message
            });
            return undefined;
        }
    }
    async getPackageInfo(rootPath) {
        const packageFiles = [
            { file: 'package.json', parser: this.parsePackageJson },
            { file: 'requirements.txt', parser: this.parseRequirementsTxt },
            { file: 'Cargo.toml', parser: this.parseCargoToml },
            { file: 'go.mod', parser: this.parseGoMod },
            { file: 'pom.xml', parser: this.parsePomXml }
        ];
        for (const { file, parser } of packageFiles) {
            const filePath = path_1.default.join(rootPath, file);
            if (await fs_extra_1.default.pathExists(filePath)) {
                try {
                    return await parser.call(this, filePath);
                }
                catch (error) {
                    this.logger.debug(`Failed to parse ${file}`, {
                        filePath,
                        error: error.message
                    });
                }
            }
        }
        return undefined;
    }
    async parsePackageJson(filePath) {
        const content = await fs_extra_1.default.readJson(filePath);
        return {
            name: content.name || 'unknown',
            version: content.version || '0.0.0',
            dependencies: content.dependencies || {},
            devDependencies: content.devDependencies || {},
            scripts: content.scripts || {}
        };
    }
    async parseRequirementsTxt(filePath) {
        const content = await fs_extra_1.default.readFile(filePath, 'utf8');
        const dependencies = {};
        for (const line of content.split('\n')) {
            const trimmed = line.trim();
            if (trimmed && !trimmed.startsWith('#')) {
                const match = trimmed.match(/^([^=<>!]+)([=<>!].+)?$/);
                if (match) {
                    dependencies[match[1].trim()] = match[2]?.trim() || '*';
                }
            }
        }
        return {
            name: path_1.default.basename(path_1.default.dirname(filePath)),
            version: '0.0.0',
            dependencies,
            devDependencies: {},
            scripts: {}
        };
    }
    async parseCargoToml(filePath) {
        // This would require a TOML parser in a real implementation
        // For now, return basic info
        return {
            name: 'rust-project',
            version: '0.1.0',
            dependencies: {},
            devDependencies: {},
            scripts: {}
        };
    }
    async parseGoMod(filePath) {
        const content = await fs_extra_1.default.readFile(filePath, 'utf8');
        const dependencies = {};
        const lines = content.split('\n');
        let inRequireBlock = false;
        let moduleName = 'go-project';
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('module ')) {
                moduleName = trimmed.replace('module ', '');
            }
            else if (trimmed === 'require (') {
                inRequireBlock = true;
            }
            else if (trimmed === ')' && inRequireBlock) {
                inRequireBlock = false;
            }
            else if (inRequireBlock || trimmed.startsWith('require ')) {
                const match = trimmed.match(/^(?:require\s+)?([^\s]+)\s+([^\s]+)/);
                if (match) {
                    dependencies[match[1]] = match[2];
                }
            }
        }
        return {
            name: moduleName,
            version: '0.0.0',
            dependencies,
            devDependencies: {},
            scripts: {}
        };
    }
    async parsePomXml(filePath) {
        // This would require an XML parser in a real implementation
        // For now, return basic info
        return {
            name: 'java-project',
            version: '1.0.0',
            dependencies: {},
            devDependencies: {},
            scripts: {}
        };
    }
    extractDependencies(packageInfo) {
        if (!packageInfo) {
            return {};
        }
        return {
            ...packageInfo.dependencies,
            ...packageInfo.devDependencies
        };
    }
    async updateFileIndex(rootPath, filePath) {
        try {
            const absolutePath = path_1.default.resolve(rootPath, filePath);
            const stats = await fs_extra_1.default.stat(absolutePath);
            const fileInfo = {
                path: filePath,
                type: stats.isDirectory() ? 'directory' : 'file',
                size: stats.size,
                lastModified: stats.mtime,
                permissions: stats.mode.toString(8)
            };
            // Read content for small text files
            const config = this.configManager.getConfig().context;
            if (stats.isFile() &&
                stats.size <= config.maxFileSize &&
                this.isTextFile(absolutePath)) {
                try {
                    fileInfo.content = await fs_extra_1.default.readFile(absolutePath, 'utf8');
                }
                catch (error) {
                    // Skip files that can't be read as text
                }
            }
            return fileInfo;
        }
        catch (error) {
            this.logger.debug('Failed to update file index', {
                rootPath,
                filePath,
                error: error.message
            });
            return null;
        }
    }
}
exports.ProjectIndexer = ProjectIndexer;
//# sourceMappingURL=ProjectIndexer.js.map