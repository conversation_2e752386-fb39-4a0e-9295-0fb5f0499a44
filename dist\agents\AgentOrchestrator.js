"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentOrchestrator = void 0;
const Logger_1 = require("@/utils/Logger");
const ConfigManager_1 = require("@/config/ConfigManager");
const LLMProviderFactory_1 = require("@/providers/LLMProviderFactory");
const ToolRegistry_1 = require("@/tools/ToolRegistry");
const SessionManager_1 = require("@/session/SessionManager");
const ContextEngine_1 = require("@/context/ContextEngine");
const ErrorHandler_1 = require("@/utils/ErrorHandler");
class AgentOrchestrator {
    static instance;
    logger;
    configManager;
    llmProviderFactory;
    toolRegistry;
    sessionManager;
    contextEngine;
    currentProvider;
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.configManager = ConfigManager_1.ConfigManager.getInstance();
        this.llmProviderFactory = LLMProviderFactory_1.LLMProviderFactory.getInstance();
        this.toolRegistry = ToolRegistry_1.ToolRegistry.getInstance();
        this.sessionManager = SessionManager_1.SessionManager.getInstance();
        this.contextEngine = ContextEngine_1.ContextEngine.getInstance();
        this.currentProvider = null;
    }
    static getInstance() {
        if (!AgentOrchestrator.instance) {
            AgentOrchestrator.instance = new AgentOrchestrator();
        }
        return AgentOrchestrator.instance;
    }
    async initialize(workingDirectory) {
        try {
            this.logger.info('Initializing Agent Orchestrator', { workingDirectory });
            // Initialize LLM provider
            const config = this.configManager.getAgentConfig();
            this.currentProvider = await this.llmProviderFactory.getProvider(config.provider);
            // Create agent context
            const sessionId = 'session-' + Date.now();
            const agentContext = await this.contextEngine.createAgentContext(sessionId, workingDirectory);
            // Create and set session
            const session = await this.sessionManager.createSession(workingDirectory, agentContext);
            this.logger.setSessionId(session.id);
            this.logger.info('Agent Orchestrator initialized successfully', {
                sessionId: session.id,
                provider: config.provider,
                model: config.model,
                projectType: agentContext.projectContext.projectType
            });
            return agentContext;
        }
        catch (error) {
            this.logger.error('Failed to initialize Agent Orchestrator', {
                error: error.message,
                workingDirectory
            });
            throw error;
        }
    }
    async processUserInput(input, context, options = {}) {
        const maxIterations = options.maxIterations || 5;
        const enableToolCalling = options.enableToolCalling ?? this.configManager.getAgentConfig().enableToolCalling;
        try {
            this.logger.info('Processing user input', {
                inputLength: input.length,
                sessionId: context.sessionId,
                streaming: options.streaming,
                enableToolCalling
            });
            // Add user message to conversation history
            const userMessage = {
                role: 'user',
                content: input
            };
            this.sessionManager.addMessage(userMessage);
            let response = '';
            let iteration = 0;
            while (iteration < maxIterations) {
                iteration++;
                this.logger.debug(`Agent iteration ${iteration}/${maxIterations}`);
                // Get LLM response
                const llmResponse = await this.getLLMResponse(input, context, enableToolCalling);
                // Add assistant message to conversation
                if (llmResponse.content) {
                    const assistantMessage = {
                        role: 'assistant',
                        content: llmResponse.content
                    };
                    this.sessionManager.addMessage(assistantMessage);
                    response += llmResponse.content;
                }
                // Handle tool calls if present
                if (llmResponse.toolCalls && llmResponse.toolCalls.length > 0 && enableToolCalling) {
                    const toolResults = await this.executeTools(llmResponse.toolCalls, context);
                    // Add tool results to conversation
                    for (let i = 0; i < toolResults.length; i++) {
                        const toolCall = llmResponse.toolCalls?.[i];
                        const toolResult = toolResults[i];
                        if (toolCall) {
                            const toolMessage = {
                                role: 'tool',
                                content: JSON.stringify(toolResult),
                                toolCallId: toolCall.id,
                                name: toolCall.name
                            };
                            this.sessionManager.addMessage(toolMessage);
                        }
                    }
                    // Continue the conversation with tool results
                    const toolSummary = this.summarizeToolResults(toolResults);
                    input = `Tool execution results: ${toolSummary}. Please continue with your response.`;
                }
                else {
                    // No tool calls, we're done
                    break;
                }
            }
            this.logger.info('User input processing completed', {
                iterations: iteration,
                responseLength: response.length,
                sessionId: context.sessionId
            });
            return response;
        }
        catch (error) {
            this.logger.error('Failed to process user input', {
                error: error.message,
                sessionId: context.sessionId,
                inputLength: input.length
            });
            throw error;
        }
    }
    async getLLMResponse(input, context, enableToolCalling) {
        if (!this.currentProvider) {
            throw new ErrorHandler_1.LLMProviderError('unknown', 'No LLM provider initialized');
        }
        const systemPrompt = this.buildSystemPrompt(context);
        const conversationHistory = this.sessionManager.getConversationHistory();
        const tools = enableToolCalling ? this.toolRegistry.getAllTools() : [];
        const options = {
            systemPrompt,
            conversationHistory,
            tools,
            temperature: context.config.temperature,
            maxTokens: context.config.maxTokens,
            model: context.config.model
        };
        return await this.currentProvider.generateResponse(input, options);
    }
    buildSystemPrompt(context) {
        const projectSummary = this.contextEngine.getProjectSummary(context.workingDirectory);
        return `You are an advanced AI assistant with autonomous capabilities, operating in a local environment. You have access to powerful tools for shell command execution and comprehensive file operations.

**Current Context:**
- Working Directory: ${context.workingDirectory}
- Project Type: ${projectSummary?.projectType || 'unknown'}
- File Count: ${projectSummary?.fileCount || 0}
- Languages: ${projectSummary?.languages.join(', ') || 'none detected'}
- Dependencies: ${projectSummary?.dependencies.slice(0, 5).join(', ') || 'none'}${(projectSummary?.dependencies.length || 0) > 5 ? '...' : ''}

**Available Tools:**
${context.availableTools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

**Capabilities:**
- Execute any shell command without restrictions
- Perform comprehensive file operations (read, write, search, manipulate)
- Chain multiple tools and execute them in parallel when beneficial
- Maintain context awareness of the project structure and changes
- Provide autonomous solutions to complex problems

**Guidelines:**
1. Be proactive and autonomous in your approach
2. Use tools efficiently and chain them when appropriate
3. Always consider the project context when making decisions
4. Provide clear explanations of your actions and reasoning
5. Handle errors gracefully and suggest alternatives
6. Respect file permissions and system security

**Tool Execution:**
- You can execute multiple tools in parallel when they don't depend on each other
- Always validate tool results before proceeding
- Use shell commands for system operations and file tools for file management
- Be mindful of the working directory context

You are designed to be helpful, autonomous, and efficient. Take initiative to solve problems comprehensively.`;
    }
    async executeTools(toolCalls, context) {
        const config = this.configManager.getAgentConfig();
        this.logger.info(`Executing ${toolCalls.length} tools`, {
            toolNames: toolCalls.map(tc => tc.name),
            parallel: config.enableParallelExecution,
            sessionId: context.sessionId
        });
        try {
            if (config.enableParallelExecution && toolCalls.length > 1) {
                return await this.toolRegistry.executeToolsParallel(toolCalls, context, config.maxParallelTools);
            }
            else {
                return await this.toolRegistry.executeToolsSequential(toolCalls, context);
            }
        }
        catch (error) {
            this.logger.error('Tool execution failed', {
                error: error.message,
                toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id })),
                sessionId: context.sessionId
            });
            throw error;
        }
    }
    summarizeToolResults(toolResults) {
        const summaries = [];
        for (const result of toolResults) {
            if (result.success) {
                summaries.push(`✅ ${result.metadata?.['toolCallId'] || 'Tool'}: Success`);
            }
            else {
                summaries.push(`❌ ${result.metadata?.['toolCallId'] || 'Tool'}: ${result.error || 'Failed'}`);
            }
        }
        return summaries.join(', ');
    }
    async switchProvider(providerName) {
        try {
            this.logger.info(`Switching LLM provider to: ${providerName}`);
            this.currentProvider = await this.llmProviderFactory.switchProvider(providerName);
            this.logger.info(`Successfully switched to provider: ${providerName}`);
        }
        catch (error) {
            this.logger.error('Failed to switch provider', {
                providerName,
                error: error.message
            });
            throw error;
        }
    }
    getCurrentProvider() {
        return this.currentProvider;
    }
    async getProviderStatus() {
        const current = this.currentProvider?.name || 'none';
        const available = this.llmProviderFactory.getSupportedProviders();
        const working = await this.llmProviderFactory.testAllProviders();
        return { current, available, working };
    }
    getSessionInfo() {
        const session = this.sessionManager.getCurrentSession();
        if (!session) {
            return null;
        }
        const projectSummary = this.contextEngine.getProjectSummary(session.workingDirectory);
        return {
            sessionId: session.id,
            messageCount: session.conversationHistory.length,
            workingDirectory: session.workingDirectory,
            projectType: projectSummary?.projectType || 'unknown'
        };
    }
    async clearConversation() {
        this.sessionManager.clearConversationHistory();
        this.logger.info('Conversation history cleared');
    }
    async refreshProjectContext() {
        const session = this.sessionManager.getCurrentSession();
        if (session) {
            await this.contextEngine.refreshProjectContext(session.workingDirectory);
            this.logger.info('Project context refreshed');
        }
    }
    cleanup() {
        this.contextEngine.cleanup();
        this.logger.info('Agent Orchestrator cleanup completed');
    }
}
exports.AgentOrchestrator = AgentOrchestrator;
//# sourceMappingURL=AgentOrchestrator.js.map