"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextEngine = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const Logger_1 = require("@/utils/Logger");
const ConfigManager_1 = require("@/config/ConfigManager");
const ProjectIndexer_1 = require("./ProjectIndexer");
const ToolRegistry_1 = require("@/tools/ToolRegistry");
class ContextEngine {
    static instance;
    projectContexts;
    fileWatchers;
    logger;
    configManager;
    projectIndexer;
    toolRegistry;
    constructor() {
        this.projectContexts = new Map();
        this.fileWatchers = new Map();
        this.logger = Logger_1.Logger.getInstance();
        this.configManager = ConfigManager_1.ConfigManager.getInstance();
        this.projectIndexer = ProjectIndexer_1.ProjectIndexer.getInstance();
        this.toolRegistry = ToolRegistry_1.ToolRegistry.getInstance();
    }
    static getInstance() {
        if (!ContextEngine.instance) {
            ContextEngine.instance = new ContextEngine();
        }
        return ContextEngine.instance;
    }
    async createAgentContext(sessionId, workingDirectory) {
        try {
            this.logger.info(`Creating agent context for session: ${sessionId}`, {
                sessionId,
                workingDirectory
            });
            // Get or create project context
            const projectContext = await this.getOrCreateProjectContext(workingDirectory);
            // Create agent context
            const agentContext = {
                sessionId,
                workingDirectory,
                projectContext,
                conversationHistory: [],
                availableTools: this.toolRegistry.getAllTools(),
                config: this.configManager.getAgentConfig()
            };
            // Set up file watching if enabled
            if (this.configManager.getConfig().context.watchForChanges) {
                await this.setupFileWatching(workingDirectory, projectContext);
            }
            this.logger.info(`Agent context created successfully`, {
                sessionId,
                projectType: projectContext.projectType,
                fileCount: projectContext.files.length,
                toolCount: agentContext.availableTools.length
            });
            return agentContext;
        }
        catch (error) {
            this.logger.error('Failed to create agent context', {
                sessionId,
                workingDirectory,
                error: error.message
            });
            throw error;
        }
    }
    async getOrCreateProjectContext(workingDirectory) {
        const normalizedPath = path_1.default.resolve(workingDirectory);
        // Check if we already have a context for this directory
        let projectContext = this.projectContexts.get(normalizedPath);
        if (!projectContext) {
            // Create new project context
            projectContext = await this.projectIndexer.indexProject(normalizedPath);
            this.projectContexts.set(normalizedPath, projectContext);
        }
        else {
            // Check if we need to refresh the context
            if (await this.shouldRefreshContext(projectContext)) {
                this.logger.info(`Refreshing project context: ${normalizedPath}`);
                projectContext = await this.projectIndexer.indexProject(normalizedPath);
                this.projectContexts.set(normalizedPath, projectContext);
            }
        }
        return projectContext;
    }
    async shouldRefreshContext(projectContext) {
        // Check if key files have been modified
        const keyFiles = [
            'package.json',
            'requirements.txt',
            'Cargo.toml',
            'go.mod',
            'pom.xml',
            '.gitignore'
        ];
        for (const keyFile of keyFiles) {
            const filePath = path_1.default.join(projectContext.rootPath, keyFile);
            try {
                if (await fs_extra_1.default.pathExists(filePath)) {
                    const stats = await fs_extra_1.default.stat(filePath);
                    const existingFile = projectContext.files.find(f => f.path === keyFile);
                    if (!existingFile || stats.mtime > existingFile.lastModified) {
                        return true;
                    }
                }
            }
            catch (error) {
                // Ignore errors checking individual files
            }
        }
        return false;
    }
    async setupFileWatching(workingDirectory, projectContext) {
        const normalizedPath = path_1.default.resolve(workingDirectory);
        // Clean up existing watcher
        if (this.fileWatchers.has(normalizedPath)) {
            this.fileWatchers.get(normalizedPath)?.close();
        }
        try {
            const config = this.configManager.getConfig().context;
            const watcher = fs_extra_1.default.watch(normalizedPath, { recursive: true }, (eventType, filename) => {
                if (filename) {
                    this.handleFileChange(normalizedPath, filename, eventType, projectContext);
                }
            });
            this.fileWatchers.set(normalizedPath, watcher);
            this.logger.debug(`File watching enabled for: ${normalizedPath}`);
        }
        catch (error) {
            this.logger.warn('Failed to setup file watching', {
                workingDirectory: normalizedPath,
                error: error.message
            });
        }
    }
    async handleFileChange(rootPath, filename, eventType, projectContext) {
        try {
            const config = this.configManager.getConfig().context;
            // Check if file should be ignored
            const shouldIgnore = config.excludePatterns.some(pattern => {
                // Simple pattern matching - in a real implementation, use a proper glob matcher
                return filename.includes(pattern.replace('**/', '').replace('*', ''));
            });
            if (shouldIgnore) {
                return;
            }
            this.logger.debug('File change detected', {
                rootPath,
                filename,
                eventType
            });
            // Update the file in the project context
            if (eventType === 'change' || eventType === 'rename') {
                const updatedFile = await this.projectIndexer.updateFileIndex(rootPath, filename);
                if (updatedFile) {
                    // Update or add the file in the project context
                    const existingIndex = projectContext.files.findIndex(f => f.path === filename);
                    if (existingIndex >= 0) {
                        projectContext.files[existingIndex] = updatedFile;
                    }
                    else {
                        projectContext.files.push(updatedFile);
                    }
                }
                else if (eventType === 'rename') {
                    // File might have been deleted
                    projectContext.files = projectContext.files.filter(f => f.path !== filename);
                }
            }
            // If it's a key file, trigger a full refresh
            const keyFiles = ['package.json', 'requirements.txt', 'Cargo.toml', 'go.mod', 'pom.xml'];
            if (keyFiles.includes(filename)) {
                this.logger.info(`Key file changed, refreshing project context: ${filename}`);
                const refreshedContext = await this.projectIndexer.indexProject(rootPath);
                this.projectContexts.set(rootPath, refreshedContext);
            }
        }
        catch (error) {
            this.logger.debug('Error handling file change', {
                rootPath,
                filename,
                eventType,
                error: error.message
            });
        }
    }
    getProjectContext(workingDirectory) {
        const normalizedPath = path_1.default.resolve(workingDirectory);
        return this.projectContexts.get(normalizedPath);
    }
    async refreshProjectContext(workingDirectory) {
        const normalizedPath = path_1.default.resolve(workingDirectory);
        this.logger.info(`Manually refreshing project context: ${normalizedPath}`);
        const projectContext = await this.projectIndexer.indexProject(normalizedPath);
        this.projectContexts.set(normalizedPath, projectContext);
        return projectContext;
    }
    searchFiles(workingDirectory, query, options = {}) {
        const projectContext = this.getProjectContext(workingDirectory);
        if (!projectContext) {
            return [];
        }
        const results = [];
        const maxResults = options.maxResults || 50;
        for (const file of projectContext.files) {
            if (results.length >= maxResults) {
                break;
            }
            // Filter by file type if specified
            if (options.fileTypes && options.fileTypes.length > 0) {
                const ext = path_1.default.extname(file.path).toLowerCase();
                if (!options.fileTypes.includes(ext)) {
                    continue;
                }
            }
            // Search in file path
            const searchText = options.caseSensitive ? file.path : file.path.toLowerCase();
            const searchQuery = options.caseSensitive ? query : query.toLowerCase();
            let matches = false;
            if (options.regex) {
                try {
                    const regex = new RegExp(query, options.caseSensitive ? 'g' : 'gi');
                    matches = regex.test(file.path) || (file.content && regex.test(file.content));
                }
                catch (error) {
                    // Invalid regex, fall back to simple search
                    matches = searchText.includes(searchQuery) ||
                        (file.content && file.content.toLowerCase().includes(searchQuery));
                }
            }
            else {
                matches = searchText.includes(searchQuery) ||
                    (file.content &&
                        (options.caseSensitive ? file.content : file.content.toLowerCase())
                            .includes(searchQuery));
            }
            if (matches) {
                results.push(file);
            }
        }
        return results;
    }
    getFileContent(workingDirectory, filePath) {
        const projectContext = this.getProjectContext(workingDirectory);
        if (!projectContext) {
            return undefined;
        }
        const file = projectContext.files.find(f => f.path === filePath);
        return file?.content;
    }
    getProjectSummary(workingDirectory) {
        const projectContext = this.getProjectContext(workingDirectory);
        if (!projectContext) {
            return undefined;
        }
        const fileCount = projectContext.files.filter(f => f.type === 'file').length;
        const totalSize = projectContext.files.reduce((sum, f) => sum + f.size, 0);
        const languages = new Set();
        for (const file of projectContext.files) {
            const ext = path_1.default.extname(file.path).toLowerCase();
            if (ext) {
                languages.add(ext.substring(1)); // Remove the dot
            }
        }
        const dependencies = Object.keys(projectContext.dependencies);
        return {
            projectType: projectContext.projectType,
            fileCount,
            totalSize,
            languages: Array.from(languages),
            dependencies,
            hasGit: !!projectContext.gitInfo
        };
    }
    cleanup() {
        // Close all file watchers
        for (const [path, watcher] of this.fileWatchers) {
            try {
                watcher.close();
                this.logger.debug(`Closed file watcher for: ${path}`);
            }
            catch (error) {
                this.logger.warn(`Failed to close file watcher for: ${path}`, {
                    error: error.message
                });
            }
        }
        this.fileWatchers.clear();
        this.projectContexts.clear();
        this.logger.info('Context engine cleanup completed');
    }
}
exports.ContextEngine = ContextEngine;
//# sourceMappingURL=ContextEngine.js.map