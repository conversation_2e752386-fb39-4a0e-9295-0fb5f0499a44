#!/usr/bin/env node

import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import dotenv from 'dotenv';
import path from 'path';
import { AgentOrchestrator } from '@/agents/AgentOrchestrator';
import { ConfigManager } from '@/config/ConfigManager';
import { SessionManager } from '@/session/SessionManager';
import { ErrorHandler } from '@/utils/ErrorHandler';
import { Logger } from '@/utils/Logger';

// Load environment variables
dotenv.config();

const program = new Command();
// const logger = Logger.getInstance();
const errorHandler = ErrorHandler.getInstance();
const configManager = ConfigManager.getInstance();
const sessionManager = SessionManager.getInstance();
const agentOrchestrator = AgentOrchestrator.getInstance();

// Setup graceful shutdown
errorHandler.createGracefulShutdown();

program
  .name('ai-cli')
  .description('Autonomous AI-Powered CLI Tool System')
  .version('1.0.0');

// Main chat command
program
  .command('chat')
  .description('Start an interactive AI chat session')
  .option('-d, --directory <path>', 'Working directory', process.cwd())
  .option('-p, --provider <provider>', 'LLM provider to use')
  .option('-m, --model <model>', 'Model to use')
  .option('-s, --session <sessionId>', 'Resume existing session')
  .action(async (options) => {
    const spinner = ora('Initializing AI CLI...').start();
    
    try {
      // Set working directory
      const workingDirectory = path.resolve(options.directory);
      process.chdir(workingDirectory);

      // Update config if provider/model specified
      if (options.provider) {
        configManager.updateAgentConfig({ provider: options.provider });
      }
      if (options.model) {
        configManager.updateAgentConfig({ model: options.model });
      }

      // Validate configuration
      if (!configManager.validateConfig()) {
        spinner.fail('Configuration validation failed');
        console.log(chalk.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
        return;
      }

      // Initialize or resume session
      let context;
      if (options.session) {
        spinner.text = 'Resuming session...';
        const session = await sessionManager.loadSession(options.session);
        context = session.context;
      } else {
        spinner.text = 'Creating new session...';
        context = await agentOrchestrator.initialize(workingDirectory);
      }

      spinner.succeed('AI CLI initialized successfully!');

      // Display welcome message
      console.log(chalk.blue.bold('\n🤖 AI CLI Agent - Autonomous Assistant'));
      console.log(chalk.gray('Type "help" for commands, "exit" to quit\n'));

      const sessionInfo = agentOrchestrator.getSessionInfo();
      if (sessionInfo) {
        console.log(chalk.cyan(`📁 Working Directory: ${sessionInfo.workingDirectory}`));
        console.log(chalk.cyan(`🏗️  Project Type: ${sessionInfo.projectType}`));
        console.log(chalk.cyan(`💬 Session: ${sessionInfo.sessionId.substring(0, 8)}...`));
        console.log('');
      }

      // Start interactive chat loop
      await startChatLoop(context);

    } catch (error) {
      spinner.fail('Failed to initialize AI CLI');
      errorHandler.handleError(error as Error, 'CLI initialization');
    }
  });

// Configuration command
program
  .command('config')
  .description('Configure AI CLI settings')
  .option('--provider <provider>', 'Set default LLM provider')
  .option('--model <model>', 'Set default model')
  .option('--api-key <key>', 'Set API key for current provider')
  .option('--list', 'List current configuration')
  .action(async (options) => {
    try {
      if (options.list) {
        const config = configManager.getConfig();
        console.log(chalk.blue.bold('\n🔧 Current Configuration:'));
        console.log(chalk.cyan(`Provider: ${config.agent.provider}`));
        console.log(chalk.cyan(`Model: ${config.agent.model}`));
        console.log(chalk.cyan(`Temperature: ${config.agent.temperature}`));
        console.log(chalk.cyan(`Max Tokens: ${config.agent.maxTokens}`));
        console.log(chalk.cyan(`Tool Calling: ${config.agent.enableToolCalling ? 'Enabled' : 'Disabled'}`));
        console.log(chalk.cyan(`Parallel Execution: ${config.agent.enableParallelExecution ? 'Enabled' : 'Disabled'}`));
        return;
      }

      if (options.provider) {
        configManager.updateAgentConfig({ provider: options.provider });
        console.log(chalk.green(`✅ Provider set to: ${options.provider}`));
      }

      if (options.model) {
        configManager.updateAgentConfig({ model: options.model });
        console.log(chalk.green(`✅ Model set to: ${options.model}`));
      }

      if (options.apiKey) {
        const provider = options.provider || configManager.getAgentConfig().provider;
        configManager.setProviderApiKey(provider, options.apiKey);
        console.log(chalk.green(`✅ API key set for: ${provider}`));
      }

      if (!options.provider && !options.model && !options.apiKey) {
        await interactiveConfig();
      }

    } catch (error) {
      errorHandler.handleError(error as Error, 'Configuration');
    }
  });

// Session management commands
program
  .command('sessions')
  .description('Manage chat sessions')
  .option('--list', 'List all sessions')
  .option('--delete <sessionId>', 'Delete a session')
  .option('--export <sessionId>', 'Export a session')
  .option('--import <filePath>', 'Import a session')
  .action(async (options) => {
    try {
      if (options.list) {
        const sessions = sessionManager.listSessions();
        console.log(chalk.blue.bold('\n📝 Chat Sessions:'));
        
        if (sessions.length === 0) {
          console.log(chalk.gray('No sessions found.'));
          return;
        }

        for (const session of sessions.slice(0, 10)) {
          const age = Math.floor((Date.now() - session.lastAccessedAt.getTime()) / (1000 * 60 * 60));
          console.log(chalk.cyan(`${session.id.substring(0, 8)}... - ${session.workingDirectory} (${age}h ago)`));
        }
        return;
      }

      if (options.delete) {
        const deleted = await sessionManager.deleteSession(options.delete);
        if (deleted) {
          console.log(chalk.green(`✅ Session deleted: ${options.delete}`));
        } else {
          console.log(chalk.red(`❌ Session not found: ${options.delete}`));
        }
        return;
      }

      if (options.export) {
        const filePath = `session-${options.export.substring(0, 8)}.json`;
        await sessionManager.exportSession(options.export, filePath);
        console.log(chalk.green(`✅ Session exported to: ${filePath}`));
        return;
      }

      if (options.import) {
        const session = await sessionManager.importSession(options.import);
        console.log(chalk.green(`✅ Session imported: ${session.id}`));
        return;
      }

    } catch (error) {
      errorHandler.handleError(error as Error, 'Session management');
    }
  });

// Status command
program
  .command('status')
  .description('Show AI CLI status and diagnostics')
  .action(async () => {
    try {
      const spinner = ora('Checking status...').start();
      
      const providerStatus = await agentOrchestrator.getProviderStatus();
      const sessionStats = sessionManager.getSessionStats();
      
      spinner.succeed('Status check completed');
      
      console.log(chalk.blue.bold('\n🔍 AI CLI Status:'));
      console.log(chalk.cyan(`Current Provider: ${providerStatus.current}`));
      console.log(chalk.cyan(`Available Providers: ${providerStatus.available.join(', ')}`));
      
      console.log(chalk.blue.bold('\n🔌 Provider Status:'));
      for (const [provider, working] of Object.entries(providerStatus.working)) {
        const status = working ? chalk.green('✅ Working') : chalk.red('❌ Not working');
        console.log(chalk.cyan(`${provider}: ${status}`));
      }
      
      console.log(chalk.blue.bold('\n📊 Session Statistics:'));
      console.log(chalk.cyan(`Total Sessions: ${sessionStats.totalSessions}`));
      console.log(chalk.cyan(`Active Sessions: ${sessionStats.activeSessions}`));
      console.log(chalk.cyan(`Total Messages: ${sessionStats.totalMessages}`));
      
    } catch (error) {
      errorHandler.handleError(error as Error, 'Status check');
    }
  });

async function startChatLoop(context: any): Promise<void> {
  while (true) {
    try {
      const { input } = await inquirer.prompt({
        type: 'input',
        name: 'input',
        message: chalk.green('You:')
      });

      if (!input.trim()) continue;

      // Handle special commands
      if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
        console.log(chalk.yellow('👋 Goodbye!'));
        break;
      }

      if (input.toLowerCase() === 'help') {
        showHelpMessage();
        continue;
      }

      if (input.toLowerCase() === 'clear') {
        await agentOrchestrator.clearConversation();
        console.log(chalk.yellow('🧹 Conversation cleared'));
        continue;
      }

      if (input.toLowerCase() === 'refresh') {
        await agentOrchestrator.refreshProjectContext();
        console.log(chalk.yellow('🔄 Project context refreshed'));
        continue;
      }

      // Process user input
      const spinner = ora('AI is thinking...').start();
      
      try {
        const response = await agentOrchestrator.processUserInput(input, context, {
          enableToolCalling: true,
          maxIterations: 5
        });

        spinner.stop();
        console.log(chalk.blue('\nAI:'), response);
        console.log('');

      } catch (error) {
        spinner.fail('AI processing failed');
        errorHandler.handleError(error as Error, 'AI processing');
      }

    } catch (error) {
      if ((error as any).name === 'ExitPromptError') {
        console.log(chalk.yellow('\n👋 Goodbye!'));
        break;
      }
      errorHandler.handleError(error as Error, 'Chat loop');
    }
  }

  // Cleanup
  agentOrchestrator.cleanup();
}

function showHelpMessage(): void {
  console.log(chalk.blue.bold('\n🆘 Available Commands:'));
  console.log(chalk.cyan('help     - Show this help message'));
  console.log(chalk.cyan('clear    - Clear conversation history'));
  console.log(chalk.cyan('refresh  - Refresh project context'));
  console.log(chalk.cyan('exit     - Exit the chat session'));
  console.log(chalk.gray('\nOr just type your question/request naturally!'));
  console.log('');
}

async function interactiveConfig(): Promise<void> {
  console.log(chalk.blue.bold('\n🔧 Interactive Configuration'));
  
  const { provider } = await inquirer.prompt([
    {
      type: 'list',
      name: 'provider',
      message: 'Select LLM provider:',
      choices: ['openai', 'anthropic', 'deepseek', 'ollama', 'gemini', 'mistral']
    }
  ]);

  configManager.updateAgentConfig({ provider });

  if (provider !== 'ollama') {
    const { apiKey } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: `Enter API key for ${provider}:`,
        mask: '*'
      }
    ]);

    configManager.setProviderApiKey(provider, apiKey);
  }

  console.log(chalk.green('\n✅ Configuration saved successfully!'));
}

// Parse command line arguments
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
