export declare class Logger {
    private static instance;
    private logger;
    private sessionId?;
    private constructor();
    static getInstance(): Logger;
    private initializeLogger;
    setSessionId(sessionId: string): void;
    error(message: string, metadata?: Record<string, any>): void;
    warn(message: string, metadata?: Record<string, any>): void;
    info(message: string, metadata?: Record<string, any>): void;
    debug(message: string, metadata?: Record<string, any>): void;
    verbose(message: string, metadata?: Record<string, any>): void;
    logToolExecution(toolName: string, args: any, result: any, duration: number): void;
    logLLMCall(provider: string, model: string, prompt: string, response: string, usage?: any): void;
    logSessionEvent(event: string, metadata?: Record<string, any>): void;
    logError(error: Error, context?: string): void;
}
//# sourceMappingURL=Logger.d.ts.map