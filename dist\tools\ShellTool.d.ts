import { Tool, ToolResult, AgentContext } from '@/types';
export declare class ShellTool implements Tool {
    readonly name = "shell";
    readonly description = "Execute shell commands with full system access";
    readonly parameters: {
        type: "object";
        properties: {
            command: {
                type: string;
                description: string;
            };
            cwd: {
                type: string;
                description: string;
            };
            env: {
                type: string;
                description: string;
            };
            timeout: {
                type: string;
                description: string;
            };
            shell: {
                type: string;
                description: string;
            };
            interactive: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
    private logger;
    constructor();
    execute(args: any, context: AgentContext): Promise<ToolResult>;
    private executeNonInteractive;
    private executeInteractive;
    listDirectory(path: string, context: AgentContext): Promise<ToolResult>;
    getCurrentDirectory(context: AgentContext): Promise<ToolResult>;
    checkCommandExists(commandName: string, context: AgentContext): Promise<boolean>;
    getSystemInfo(context: AgentContext): Promise<ToolResult>;
}
//# sourceMappingURL=ShellTool.d.ts.map