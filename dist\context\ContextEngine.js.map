{"version": 3, "file": "ContextEngine.js", "sourceRoot": "", "sources": ["../../src/context/ContextEngine.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAA0B;AAC1B,gDAAwB;AAExB,2CAAwC;AACxC,0DAAuD;AACvD,qDAAkD;AAClD,uDAAoD;AAEpD,MAAa,aAAa;IAChB,MAAM,CAAC,QAAQ,CAAgB;IAC/B,eAAe,CAA8B;IAC7C,YAAY,CAA4B;IACxC,MAAM,CAAS;IACf,aAAa,CAAgB;IAC7B,cAAc,CAAiB;IAC/B,YAAY,CAAe;IAEnC;QACE,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,6BAAa,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,cAAc,GAAG,+BAAc,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;IACjD,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC5B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAC7B,SAAiB,EACjB,gBAAwB;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,SAAS,EAAE,EAAE;gBACnE,SAAS;gBACT,gBAAgB;aACjB,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;YAE9E,uBAAuB;YACvB,MAAM,YAAY,GAAiB;gBACjC,SAAS;gBACT,gBAAgB;gBAChB,cAAc;gBACd,mBAAmB,EAAE,EAAE;gBACvB,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;gBAC/C,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;aAC5C,CAAC;YAEF,kCAAkC;YAClC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC3D,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBACrD,SAAS;gBACT,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,SAAS,EAAE,cAAc,CAAC,KAAK,CAAC,MAAM;gBACtC,SAAS,EAAE,YAAY,CAAC,cAAc,CAAC,MAAM;aAC9C,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,SAAS;gBACT,gBAAgB;gBAChB,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,gBAAwB;QAC9D,MAAM,cAAc,GAAG,cAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAEtD,wDAAwD;QACxD,IAAI,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAE9D,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,6BAA6B;YAC7B,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACxE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,0CAA0C;YAC1C,IAAI,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,cAAc,EAAE,CAAC,CAAC;gBAClE,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;gBACxE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,cAA8B;QAC/D,wCAAwC;QACxC,MAAM,QAAQ,GAAG;YACf,cAAc;YACd,kBAAkB;YAClB,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,YAAY;SACb,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC7D,IAAI,CAAC;gBACH,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAClC,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACtC,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;oBAExE,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;wBAC7D,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,0CAA0C;YAC5C,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,gBAAwB,EACxB,cAA8B;QAE9B,MAAM,cAAc,GAAG,cAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAEtD,4BAA4B;QAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC;QACjD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC;YAEtD,MAAM,OAAO,GAAG,kBAAE,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;gBACpF,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;gBAC7E,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAE/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,cAAc,EAAE,CAAC,CAAC;QAEpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBAChD,gBAAgB,EAAE,cAAc;gBAChC,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,QAAgB,EAChB,QAAgB,EAChB,SAAiB,EACjB,cAA8B;QAE9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC;YAEtD,kCAAkC;YAClC,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBACzD,gFAAgF;gBAChF,OAAO,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACxC,QAAQ;gBACR,QAAQ;gBACR,SAAS;aACV,CAAC,CAAC;YAEH,yCAAyC;YACzC,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;gBACrD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAElF,IAAI,WAAW,EAAE,CAAC;oBAChB,gDAAgD;oBAChD,MAAM,aAAa,GAAG,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;oBAE/E,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;wBACvB,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;oBACpD,CAAC;yBAAM,CAAC;wBACN,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACzC,CAAC;gBACH,CAAC;qBAAM,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;oBAClC,+BAA+B;oBAC/B,cAAc,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;YAED,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,CAAC,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;YACzF,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iDAAiD,QAAQ,EAAE,CAAC,CAAC;gBAC9E,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAC1E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YACvD,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEM,iBAAiB,CAAC,gBAAwB;QAC/C,MAAM,cAAc,GAAG,cAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAClD,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,gBAAwB;QACzD,MAAM,cAAc,GAAG,cAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAEtD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,cAAc,EAAE,CAAC,CAAC;QAE3E,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAC9E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAEzD,OAAO,cAAc,CAAC;IACxB,CAAC;IAEM,WAAW,CAChB,gBAAwB,EACxB,KAAa,EACb,UAKI,EAAE;QAEN,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,OAAO,GAAe,EAAE,CAAC;QAC/B,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAE5C,KAAK,MAAM,IAAI,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;gBACjC,MAAM;YACR,CAAC;YAED,mCAAmC;YACnC,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtD,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBAClD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrC,SAAS;gBACX,CAAC;YACH,CAAC;YAED,sBAAsB;YACtB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC/E,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAExE,IAAI,OAAO,GAAG,KAAK,CAAC;YAEpB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClB,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBACpE,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBACvF,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,4CAA4C;oBAC5C,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC;wBACjC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBACrF,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC;oBACjC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACd,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;6BAChE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,cAAc,CAAC,gBAAwB,EAAE,QAAgB;QAC9D,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,IAAI,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QACjE,OAAO,IAAI,EAAE,OAAO,CAAC;IACvB,CAAC;IAEM,iBAAiB,CAAC,gBAAwB;QAQ/C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAC7E,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE3E,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,KAAK,MAAM,IAAI,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;YACxC,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,IAAI,GAAG,EAAE,CAAC;gBACR,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;YACpD,CAAC;QACH,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAE9D,OAAO;YACL,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,SAAS;YACT,SAAS;YACT,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;YAChC,YAAY;YACZ,MAAM,EAAE,CAAC,CAAC,cAAc,CAAC,OAAO;SACjC,CAAC;IACJ,CAAC;IAEM,OAAO;QACZ,0BAA0B;QAC1B,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAChD,IAAI,CAAC;gBACH,OAAO,CAAC,KAAK,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;YACxD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,IAAI,EAAE,EAAE;oBAC5D,KAAK,EAAG,KAAe,CAAC,OAAO;iBAChC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAE7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IACvD,CAAC;CACF;AAvWD,sCAuWC"}