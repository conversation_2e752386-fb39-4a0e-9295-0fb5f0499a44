{"version": 3, "file": "ErrorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": ";;;AAAA,qCAAkC;AAElC,MAAa,UAAW,SAAQ,KAAK;IACnB,IAAI,CAAS;IACb,OAAO,CAAuB;IAE9C,YAAY,OAAe,EAAE,IAAY,EAAE,OAA6B;QACtE,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AAVD,gCAUC;AAED,MAAa,kBAAmB,SAAQ,UAAU;IAChD,YAAY,QAAgB,EAAE,OAAe,EAAE,OAA6B;QAC1E,KAAK,CAAC,0BAA0B,QAAQ,MAAM,OAAO,EAAE,EAAE,sBAAsB,EAAE;YAC/E,QAAQ;YACR,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;CACF;AAPD,gDAOC;AAED,MAAa,gBAAiB,SAAQ,UAAU;IAC9C,YAAY,QAAgB,EAAE,OAAe,EAAE,OAA6B;QAC1E,KAAK,CAAC,uBAAuB,QAAQ,MAAM,OAAO,EAAE,EAAE,oBAAoB,EAAE;YAC1E,QAAQ;YACR,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;CACF;AAPD,4CAOC;AAED,MAAa,YAAa,SAAQ,UAAU;IAC1C,YAAY,OAAe,EAAE,OAA6B;QACxD,KAAK,CAAC,kBAAkB,OAAO,EAAE,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;CACF;AAJD,oCAIC;AAED,MAAa,kBAAmB,SAAQ,UAAU;IAChD,YAAY,OAAe,EAAE,OAA6B;QACxD,KAAK,CAAC,wBAAwB,OAAO,EAAE,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAC3E,CAAC;CACF;AAJD,gDAIC;AAED,MAAa,kBAAmB,SAAQ,UAAU;IAChD,YAAY,SAAiB,EAAE,IAAY,EAAE,OAAe,EAAE,OAA6B;QACzF,KAAK,CAAC,0BAA0B,SAAS,OAAO,IAAI,MAAM,OAAO,EAAE,EAAE,sBAAsB,EAAE;YAC3F,SAAS;YACT,IAAI;YACJ,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;CACF;AARD,gDAQC;AAED,MAAa,mBAAoB,SAAQ,UAAU;IACjD,YAAY,OAAe,EAAE,QAAgB,EAAE,MAAc,EAAE,OAA6B;QAC1F,KAAK,CAAC,yBAAyB,OAAO,gBAAgB,QAAQ,GAAG,EAAE,uBAAuB,EAAE;YAC1F,OAAO;YACP,QAAQ;YACR,MAAM;YACN,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;CACF;AATD,kDASC;AAED,MAAa,YAAY;IACf,MAAM,CAAC,QAAQ,CAAe;IAC9B,MAAM,CAAS;IAEvB;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC7C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAEM,WAAW,CAAC,KAAY,EAAE,OAAgB;QAC/C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAErC,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;YAChC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,KAAiB;QACxC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,sBAAsB;gBACzB,OAAO,CAAC,KAAK,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACjD,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC;oBAC5B,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACtD,CAAC;gBACD,MAAM;YAER,KAAK,oBAAoB;gBACvB,OAAO,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACzD,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC;oBAC5B,OAAO,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC1D,CAAC;gBACD,MAAM;YAER,KAAK,eAAe;gBAClB,OAAO,CAAC,KAAK,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACpD,MAAM;YAER,KAAK,qBAAqB;gBACxB,OAAO,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC3D,MAAM;YAER,KAAK,sBAAsB;gBACzB,OAAO,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC3D,IAAI,KAAK,CAAC,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;oBACpD,OAAO,CAAC,KAAK,CAAC,iBAAiB,KAAK,CAAC,OAAO,CAAC,SAAS,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;gBACrF,CAAC;gBACD,MAAM;YAER,KAAK,uBAAuB;gBAC1B,OAAO,CAAC,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC5D,IAAI,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;oBAC3B,OAAO,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxD,CAAC;gBACD,IAAI,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;oBAC1B,OAAO,CAAC,KAAK,CAAC,oBAAoB,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC5D,CAAC;gBACD,MAAM;YAER;gBACE,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAC5D,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,KAAY,EAAE,OAAgB;QACvD,OAAO,CAAC,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,KAAK,CAAC,eAAe,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,SAA2B,EAC3B,OAAgB;QAEhB,IAAI,CAAC;YACH,OAAO,MAAM,SAAS,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAc,EAAE,OAAO,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEM,eAAe,CACpB,SAAkB,EAClB,OAAgB;QAEhB,IAAI,CAAC;YACH,OAAO,SAAS,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAc,EAAE,OAAO,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEM,sBAAsB;QAC3B,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;YACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YACtF,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;YACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;YAC9D,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC9D,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC/D,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAtID,oCAsIC"}