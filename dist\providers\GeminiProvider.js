"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeminiProvider = void 0;
const generative_ai_1 = require("@google/generative-ai");
const ErrorHandler_1 = require("@/utils/ErrorHandler");
const Logger_1 = require("@/utils/Logger");
const ToolRegistry_1 = require("@/tools/ToolRegistry");
class GeminiProvider {
    name = 'gemini';
    client;
    logger;
    toolRegistry;
    constructor(config) {
        this.logger = Logger_1.Logger.getInstance();
        this.toolRegistry = ToolRegistry_1.ToolRegistry.getInstance();
        if (!config.apiKey) {
            throw new ErrorHandler_1.LLMProviderError(this.name, 'Google Gemini API key is required');
        }
        this.client = new generative_ai_1.GoogleGenerativeAI(config.apiKey);
    }
    async generateResponse(prompt, options = {}) {
        try {
            const model = options.model || 'gemini-pro';
            const genModel = this.client.getGenerativeModel({ model });
            const generationConfig = {
                temperature: options.temperature || 0.7,
                maxOutputTokens: options.maxTokens || 4000
            };
            this.logger.debug('Making Gemini API request', {
                model,
                prompt: prompt.substring(0, 100) + '...'
            });
            const result = await genModel.generateContent({
                contents: [{ role: 'user', parts: [{ text: prompt }] }],
                generationConfig
            });
            const response = result.response;
            const content = response.text() || '';
            const llmResponse = {
                content,
                toolCalls: [], // Basic implementation - Gemini function calling would need more work
                usage: response.usageMetadata ? {
                    promptTokens: response.usageMetadata.promptTokenCount || 0,
                    completionTokens: response.usageMetadata.candidatesTokenCount || 0,
                    totalTokens: response.usageMetadata.totalTokenCount || 0
                } : undefined
            };
            this.logger.logLLMCall(this.name, model, prompt, content, llmResponse.usage);
            return llmResponse;
        }
        catch (error) {
            this.logger.error('Gemini API request failed', {
                error: error.message,
                prompt: prompt.substring(0, 100) + '...'
            });
            throw new ErrorHandler_1.LLMProviderError(this.name, error.message);
        }
    }
    async *generateStreamResponse(prompt, options = {}) {
        try {
            const model = options.model || 'gemini-pro';
            const genModel = this.client.getGenerativeModel({ model });
            const generationConfig = {
                temperature: options.temperature || 0.7,
                maxOutputTokens: options.maxTokens || 4000
            };
            this.logger.debug('Making Gemini streaming API request', {
                model,
                prompt: prompt.substring(0, 100) + '...'
            });
            const result = await genModel.generateContentStream({
                contents: [{ role: 'user', parts: [{ text: prompt }] }],
                generationConfig
            });
            for await (const chunk of result.stream) {
                const chunkText = chunk.text();
                if (chunkText) {
                    yield chunkText;
                }
            }
        }
        catch (error) {
            this.logger.error('Gemini streaming API request failed', {
                error: error.message,
                prompt: prompt.substring(0, 100) + '...'
            });
            throw new ErrorHandler_1.LLMProviderError(this.name, error.message);
        }
    }
    supportsToolCalling() {
        return false; // Basic implementation - could be extended
    }
    async callTool(toolCall, context) {
        throw new ErrorHandler_1.LLMProviderError(this.name, 'Tool calling not implemented for Gemini provider');
    }
    async validateApiKey() {
        try {
            const model = this.client.getGenerativeModel({ model: 'gemini-pro' });
            await model.generateContent('Hello');
            return true;
        }
        catch (error) {
            this.logger.error('Gemini API key validation failed', {
                error: error.message
            });
            return false;
        }
    }
    async getAvailableModels() {
        // Gemini doesn't have a models endpoint, so return known models
        return ['gemini-pro', 'gemini-pro-vision'];
    }
    getDefaultModel() {
        return 'gemini-pro';
    }
    getMaxTokens(model) {
        const tokenLimits = {
            'gemini-pro': 32000,
            'gemini-pro-vision': 32000
        };
        return tokenLimits[model || 'gemini-pro'] || 32000;
    }
    estimateTokens(text) {
        // Rough estimation: 1 token ≈ 4 characters for English text
        return Math.ceil(text.length / 4);
    }
    calculateCost(usage, model) {
        // Gemini pricing (in USD per 1M tokens)
        const pricing = {
            'gemini-pro': { input: 0.5, output: 1.5 },
            'gemini-pro-vision': { input: 0.5, output: 1.5 }
        };
        const modelPricing = pricing[model || 'gemini-pro'] || pricing['gemini-pro'];
        const inputCost = (usage.promptTokens / 1000000) * modelPricing.input;
        const outputCost = (usage.completionTokens / 1000000) * modelPricing.output;
        return inputCost + outputCost;
    }
}
exports.GeminiProvider = GeminiProvider;
//# sourceMappingURL=GeminiProvider.js.map