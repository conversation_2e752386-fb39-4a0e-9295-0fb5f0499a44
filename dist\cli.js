#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const AgentOrchestrator_1 = require("@/agents/AgentOrchestrator");
const ConfigManager_1 = require("@/config/ConfigManager");
const SessionManager_1 = require("@/session/SessionManager");
const ErrorHandler_1 = require("@/utils/ErrorHandler");
// Load environment variables
dotenv_1.default.config();
const program = new commander_1.Command();
// const logger = Logger.getInstance();
const errorHandler = ErrorHandler_1.ErrorHandler.getInstance();
const configManager = ConfigManager_1.ConfigManager.getInstance();
const sessionManager = SessionManager_1.SessionManager.getInstance();
const agentOrchestrator = AgentOrchestrator_1.AgentOrchestrator.getInstance();
// Setup graceful shutdown
errorHandler.createGracefulShutdown();
program
    .name('ai-cli')
    .description('Autonomous AI-Powered CLI Tool System')
    .version('1.0.0');
// Main chat command
program
    .command('chat')
    .description('Start an interactive AI chat session')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('-m, --model <model>', 'Model to use')
    .option('-s, --session <sessionId>', 'Resume existing session')
    .action(async (options) => {
    const spinner = (0, ora_1.default)('Initializing AI CLI...').start();
    try {
        // Set working directory
        const workingDirectory = path_1.default.resolve(options.directory);
        process.chdir(workingDirectory);
        // Update config if provider/model specified
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (options.model) {
            configManager.updateAgentConfig({ model: options.model });
        }
        // Validate configuration
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk_1.default.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        // Initialize or resume session
        let context;
        if (options.session) {
            spinner.text = 'Resuming session...';
            const session = await sessionManager.loadSession(options.session);
            context = session.context;
        }
        else {
            spinner.text = 'Creating new session...';
            context = await agentOrchestrator.initialize(workingDirectory);
        }
        spinner.succeed('AI CLI initialized successfully!');
        // Display welcome message
        console.log(chalk_1.default.blue.bold('\n🤖 AI CLI Agent - Autonomous Assistant'));
        console.log(chalk_1.default.gray('Type "help" for commands, "exit" to quit\n'));
        const sessionInfo = agentOrchestrator.getSessionInfo();
        if (sessionInfo) {
            console.log(chalk_1.default.cyan(`📁 Working Directory: ${sessionInfo.workingDirectory}`));
            console.log(chalk_1.default.cyan(`🏗️  Project Type: ${sessionInfo.projectType}`));
            console.log(chalk_1.default.cyan(`💬 Session: ${sessionInfo.sessionId.substring(0, 8)}...`));
            console.log('');
        }
        // Start interactive chat loop
        await startChatLoop(context);
    }
    catch (error) {
        spinner.fail('Failed to initialize AI CLI');
        errorHandler.handleError(error, 'CLI initialization');
    }
});
// Configuration command
program
    .command('config')
    .description('Configure AI CLI settings')
    .option('--provider <provider>', 'Set default LLM provider')
    .option('--model <model>', 'Set default model')
    .option('--api-key <key>', 'Set API key for current provider')
    .option('--list', 'List current configuration')
    .action(async (options) => {
    try {
        if (options.list) {
            const config = configManager.getConfig();
            console.log(chalk_1.default.blue.bold('\n🔧 Current Configuration:'));
            console.log(chalk_1.default.cyan(`Provider: ${config.agent.provider}`));
            console.log(chalk_1.default.cyan(`Model: ${config.agent.model}`));
            console.log(chalk_1.default.cyan(`Temperature: ${config.agent.temperature}`));
            console.log(chalk_1.default.cyan(`Max Tokens: ${config.agent.maxTokens}`));
            console.log(chalk_1.default.cyan(`Tool Calling: ${config.agent.enableToolCalling ? 'Enabled' : 'Disabled'}`));
            console.log(chalk_1.default.cyan(`Parallel Execution: ${config.agent.enableParallelExecution ? 'Enabled' : 'Disabled'}`));
            return;
        }
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
            console.log(chalk_1.default.green(`✅ Provider set to: ${options.provider}`));
        }
        if (options.model) {
            configManager.updateAgentConfig({ model: options.model });
            console.log(chalk_1.default.green(`✅ Model set to: ${options.model}`));
        }
        if (options.apiKey) {
            const provider = options.provider || configManager.getAgentConfig().provider;
            configManager.setProviderApiKey(provider, options.apiKey);
            console.log(chalk_1.default.green(`✅ API key set for: ${provider}`));
        }
        if (!options.provider && !options.model && !options.apiKey) {
            await interactiveConfig();
        }
    }
    catch (error) {
        errorHandler.handleError(error, 'Configuration');
    }
});
// Session management commands
program
    .command('sessions')
    .description('Manage chat sessions')
    .option('--list', 'List all sessions')
    .option('--delete <sessionId>', 'Delete a session')
    .option('--export <sessionId>', 'Export a session')
    .option('--import <filePath>', 'Import a session')
    .action(async (options) => {
    try {
        if (options.list) {
            const sessions = sessionManager.listSessions();
            console.log(chalk_1.default.blue.bold('\n📝 Chat Sessions:'));
            if (sessions.length === 0) {
                console.log(chalk_1.default.gray('No sessions found.'));
                return;
            }
            for (const session of sessions.slice(0, 10)) {
                const age = Math.floor((Date.now() - session.lastAccessedAt.getTime()) / (1000 * 60 * 60));
                console.log(chalk_1.default.cyan(`${session.id.substring(0, 8)}... - ${session.workingDirectory} (${age}h ago)`));
            }
            return;
        }
        if (options.delete) {
            const deleted = await sessionManager.deleteSession(options.delete);
            if (deleted) {
                console.log(chalk_1.default.green(`✅ Session deleted: ${options.delete}`));
            }
            else {
                console.log(chalk_1.default.red(`❌ Session not found: ${options.delete}`));
            }
            return;
        }
        if (options.export) {
            const filePath = `session-${options.export.substring(0, 8)}.json`;
            await sessionManager.exportSession(options.export, filePath);
            console.log(chalk_1.default.green(`✅ Session exported to: ${filePath}`));
            return;
        }
        if (options.import) {
            const session = await sessionManager.importSession(options.import);
            console.log(chalk_1.default.green(`✅ Session imported: ${session.id}`));
            return;
        }
    }
    catch (error) {
        errorHandler.handleError(error, 'Session management');
    }
});
// Status command
program
    .command('status')
    .description('Show AI CLI status and diagnostics')
    .action(async () => {
    try {
        const spinner = (0, ora_1.default)('Checking status...').start();
        const providerStatus = await agentOrchestrator.getProviderStatus();
        const sessionStats = sessionManager.getSessionStats();
        spinner.succeed('Status check completed');
        console.log(chalk_1.default.blue.bold('\n🔍 AI CLI Status:'));
        console.log(chalk_1.default.cyan(`Current Provider: ${providerStatus.current}`));
        console.log(chalk_1.default.cyan(`Available Providers: ${providerStatus.available.join(', ')}`));
        console.log(chalk_1.default.blue.bold('\n🔌 Provider Status:'));
        for (const [provider, working] of Object.entries(providerStatus.working)) {
            const status = working ? chalk_1.default.green('✅ Working') : chalk_1.default.red('❌ Not working');
            console.log(chalk_1.default.cyan(`${provider}: ${status}`));
        }
        console.log(chalk_1.default.blue.bold('\n📊 Session Statistics:'));
        console.log(chalk_1.default.cyan(`Total Sessions: ${sessionStats.totalSessions}`));
        console.log(chalk_1.default.cyan(`Active Sessions: ${sessionStats.activeSessions}`));
        console.log(chalk_1.default.cyan(`Total Messages: ${sessionStats.totalMessages}`));
    }
    catch (error) {
        errorHandler.handleError(error, 'Status check');
    }
});
async function startChatLoop(context) {
    while (true) {
        try {
            const { input } = await inquirer_1.default.prompt({
                type: 'input',
                name: 'input',
                message: chalk_1.default.green('You:')
            });
            if (!input.trim())
                continue;
            // Handle special commands
            if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
                console.log(chalk_1.default.yellow('👋 Goodbye!'));
                break;
            }
            if (input.toLowerCase() === 'help') {
                showHelpMessage();
                continue;
            }
            if (input.toLowerCase() === 'clear') {
                await agentOrchestrator.clearConversation();
                console.log(chalk_1.default.yellow('🧹 Conversation cleared'));
                continue;
            }
            if (input.toLowerCase() === 'refresh') {
                await agentOrchestrator.refreshProjectContext();
                console.log(chalk_1.default.yellow('🔄 Project context refreshed'));
                continue;
            }
            // Process user input
            const spinner = (0, ora_1.default)('AI is thinking...').start();
            try {
                const response = await agentOrchestrator.processUserInput(input, context, {
                    enableToolCalling: true,
                    maxIterations: 5
                });
                spinner.stop();
                console.log(chalk_1.default.blue('\nAI:'), response);
                console.log('');
            }
            catch (error) {
                spinner.fail('AI processing failed');
                errorHandler.handleError(error, 'AI processing');
            }
        }
        catch (error) {
            if (error.name === 'ExitPromptError') {
                console.log(chalk_1.default.yellow('\n👋 Goodbye!'));
                break;
            }
            errorHandler.handleError(error, 'Chat loop');
        }
    }
    // Cleanup
    agentOrchestrator.cleanup();
}
function showHelpMessage() {
    console.log(chalk_1.default.blue.bold('\n🆘 Available Commands:'));
    console.log(chalk_1.default.cyan('help     - Show this help message'));
    console.log(chalk_1.default.cyan('clear    - Clear conversation history'));
    console.log(chalk_1.default.cyan('refresh  - Refresh project context'));
    console.log(chalk_1.default.cyan('exit     - Exit the chat session'));
    console.log(chalk_1.default.gray('\nOr just type your question/request naturally!'));
    console.log('');
}
async function interactiveConfig() {
    console.log(chalk_1.default.blue.bold('\n🔧 Interactive Configuration'));
    const { provider } = await inquirer_1.default.prompt([
        {
            type: 'list',
            name: 'provider',
            message: 'Select LLM provider:',
            choices: ['openai', 'anthropic', 'deepseek', 'ollama', 'gemini', 'mistral']
        }
    ]);
    configManager.updateAgentConfig({ provider });
    if (provider !== 'ollama') {
        const { apiKey } = await inquirer_1.default.prompt([
            {
                type: 'password',
                name: 'apiKey',
                message: `Enter API key for ${provider}:`,
                mask: '*'
            }
        ]);
        configManager.setProviderApiKey(provider, apiKey);
    }
    console.log(chalk_1.default.green('\n✅ Configuration saved successfully!'));
}
// Parse command line arguments
program.parse();
// If no command provided, show help
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
//# sourceMappingURL=cli.js.map