{"version": 3, "file": "GeminiProvider.d.ts", "sourceRoot": "", "sources": ["../../src/providers/GeminiProvider.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAKnG,qBAAa,cAAe,YAAW,WAAW;IAChD,SAAgB,IAAI,YAAY;IAChC,OAAO,CAAC,MAAM,CAAqB;IACnC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,YAAY,CAAe;gBAEvB,MAAM,EAAE,GAAG;IAWV,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,GAAG,OAAO,CAAC,WAAW,CAAC;IA+C/E,sBAAsB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,GAAG,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC;IAqC9G,mBAAmB,IAAI,OAAO;IAIxB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;IAIxE,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IAalC,kBAAkB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IAK7C,eAAe,IAAI,MAAM;IAIzB,YAAY,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM;IASpC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAKpC,aAAa,CAAC,KAAK,EAAE;QAAE,YAAY,EAAE,MAAM,CAAC;QAAC,gBAAgB,EAAE,MAAM,CAAA;KAAE,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM;CAaxG"}